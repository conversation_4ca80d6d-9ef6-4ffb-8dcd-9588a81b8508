export const DEFAULT_ROLES = [
  {
    name: 'admin',
    label: 'Administrator',
    description: 'Administrator to manage all users, roles, and permissions and the admin internal dashboard',
    permissions: [], // Will be populated with all admin:* permissions during initialization
    isInternal: true,
    canRegister: false,
  },
  {
    name: 'order_support_dashboard',
    label: 'Order Support Dashboard',
    description: 'Order support dashboard to manage orders and support requests',
    permissions: [],
    isInternal: true,
    canRegister: false,
  },
  {
    name: 'dks_owner',
    label: 'DKS Owner',
    description: 'DKS user to manage root account',
    permissions: [],
    isInternal: false,
    canRegister: true,
  },
  {
    name: 'dks_admin',
    label: 'DKS Admin',
    description: 'DKS Admin User',
    permissions: [],
    isInternal: false,
    canRegister: true,
  },
  {
    name: 'dks_developer',
    label: 'DKS Developer',
    description: 'DKS Developer User',
    permissions: [],
    isInternal: false,
    canRegister: true,
  },
  {
    name: 'dks_viewer',
    label: '<PERSON>KS Viewer',
    description: 'DKS User with Viewer Role',
    permissions: [],
    isInternal: false,
    canRegister: true,
  },
  {
    name: 'order_support_admin',
    label: 'Order and Support Admin',
    description: 'Order and Support Admin User',
    permissions: [],
    isInternal: true,
    canRegister: true,
  },
  {
    name: 'internal_dashboard_user',
    label: 'Internal Dashboard User',
    description: 'User to access the internal dashboard',
    permissions: [],
    isInternal: true,
    canRegister: true,
  },
];
