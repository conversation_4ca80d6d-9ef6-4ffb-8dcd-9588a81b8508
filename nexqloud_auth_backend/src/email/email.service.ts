import { Injectable, Logger } from '@nestjs/common';
import * as nodemailer from 'nodemailer';
import * as mg from 'nodemailer-mailgun-transport';
import * as fs from 'fs/promises';
import { MAILGUN_API_KEY, MAILGUN_DOMAIN, SMTP_EMAIL, DEV_TEAM_EMAILS, IS_LOCAL } from '../constants';
import * as path from 'path';
import { EmailTemplateName, TemplateFilePaths } from './templates.constants';

@Injectable()
export class EmailService {
  private readonly transporter: nodemailer.Transporter;
  private readonly logger = new Logger(EmailService.name);
  private templateCache: Map<string, string> = new Map();
  private readonly templatesDir: string;

  constructor() {
    const mailgunAuth = {
      auth: {
        api_key: MAILGUN_API_KEY,
        domain: MAILGUN_DOMAIN,
      },
    };

    this.transporter = nodemailer.createTransport(mg(mailgunAuth));
    this.templatesDir = path.join(process.cwd(), 'email-templates');
  }

  async onModuleInit() {
    try {
      await this.loadTemplates();
    } catch (error) {
      this.logger.error('Failed to load email templates:', error);
      throw error;
    }
  }

  private async loadTemplates(): Promise<void> {
    this.templateCache.clear();
    const entries = Object.entries(TemplateFilePaths) as [EmailTemplateName, string][];
    for (const [templateName, fileName] of entries) {
      try {
        const filePath = path.join(this.templatesDir, fileName);
        const template = await fs.readFile(filePath, 'utf-8');
        this.templateCache.set(templateName, template);
      } catch (error) {
        this.logger.error(`Failed to load template ${fileName}:`, error);
        throw error;
      }
    }
  }

  private getHtmlTemplate(templateName: EmailTemplateName): string {
    const template = this.templateCache.get(templateName);
    if (!template) {
      throw new Error(`Template ${templateName} not found in cache`);
    }
    return template;
  }

  private getRecipients(to: string | string[]): string[] {
    if (IS_LOCAL && DEV_TEAM_EMAILS && DEV_TEAM_EMAILS.length > 0) {
      return DEV_TEAM_EMAILS;
    }
    return Array.isArray(to) ? to : [to];
  }

  private getSubject(subject: string, to: string | string[]): string {
    return IS_LOCAL ? `[${process.env.NODE_ENV?.toUpperCase()}]-[Original Recipient(s): ${to}] ${subject}` : subject;
  }

  async sendEmailWithTemplate(
    to: string | string[],
    subject: string,
    templateName: EmailTemplateName,
    data: Record<string, any>,
  ): Promise<void> {
    try {
      let htmlContent = this.getHtmlTemplate(templateName);

      // Replace template variables
      Object.entries(data).forEach(([key, value]) => {
        const placeholder = `{{${key}}}`;
        htmlContent = htmlContent.replace(new RegExp(placeholder, 'g'), value.toString());
      });
      await this.send(to, subject, htmlContent);
    } catch (error) {
      this.logger.error(`Failed to send email: ${error.message}`, error.stack);
      throw error;
    }
  }

  async sendEmail(to: string | string[], subject: string, text: string): Promise<void> {
    try {
      await this.send(to, subject, text);
    } catch (error) {
      this.logger.error(`Failed to send email: ${error.message}`, error.stack);
      throw error;
    }
  }

  async sendEmailWithBody(to: string | string[], subject: string, html: string): Promise<void> {
    try {
      await this.send(to, subject, html);
    } catch (error) {
      this.logger.error(`Failed to send email with body: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async send(to: string | string[], subject: string, html: string): Promise<void> {
    const recipients = this.getRecipients(to);
    const formattedSubject = this.getSubject(subject, to);
    try {
      await this.transporter.sendMail({
        from: SMTP_EMAIL,
        to: recipients,
        subject: formattedSubject,
        html,
      });

      this.logger.log(`Email sent successfully to ${recipients.join(', ')}`);
    } catch (error) {
      this.logger.error(`Failed to send email: ${error.message}`, error.stack);
      throw error;
    }
  }
}
