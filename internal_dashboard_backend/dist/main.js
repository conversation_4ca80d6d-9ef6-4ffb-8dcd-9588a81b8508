"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
require('dotenv').config();
const core_1 = require("@nestjs/core");
const swagger_1 = require("@nestjs/swagger");
const app_module_1 = require("./app.module");
const common_1 = require("@nestjs/common");
const exception_filter_1 = require("./filters/exception-filter");
const logging_service_1 = require("./logging/logging.service");
const cookieParser = require("cookie-parser");
function setupSwagger(app) {
    const config = new swagger_1.DocumentBuilder()
        .setTitle('Internal Dashboard Backend API')
        .setDescription('The Internal Dashboard Backend API collection of endpoints')
        .setVersion(process.env.npm_package_version || '1.0')
        .addBearerAuth()
        .build();
    const customOptions = {
        swaggerOptions: {
            persistAuthorization: true,
        },
    };
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('api', app, document, customOptions);
}
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
    }));
    app.enableCors();
    app.use(cookieParser());
    const logger = app.get(logging_service_1.LoggingService);
    app.useGlobalFilters(new exception_filter_1.GlobalExceptionsFilter(logger));
    app.useLogger(process.env.NODE_ENV === 'production' ? ['error', 'warn'] : ['log', 'error', 'warn', 'debug', 'verbose']);
    if (process.env.NODE_ENV !== 'production') {
        setupSwagger(app);
    }
    await app.listen(process.env.PORT || 3006);
}
bootstrap();
//# sourceMappingURL=main.js.map