import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { SignupDto } from './dto/signup.dto';
import { VerifyOtpDto } from './dto/verify-otp.dto';
import { RefreshTokenDto } from './dto/refresh-token.dto';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    login(loginDto: LoginDto): Promise<import("./auth.service").AuthResponse>;
    signup(signupDto: SignupDto): Promise<{
        sessionId: string;
    }>;
    verifyOtp(sessionId: string, verifyOtpDto: VerifyOtpDto): Promise<import("./auth.service").AuthResponse>;
    refreshToken(refreshTokenDto: RefreshTokenDto): Promise<{
        accessToken: string;
    }>;
    logout(authorization?: string): Promise<{
        message: string;
    }>;
}
