"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const axios_1 = require("axios");
const logging_service_1 = require("../logging/logging.service");
const constants_1 = require("../constants");
let AuthService = class AuthService {
    constructor(jwtService, logger) {
        this.jwtService = jwtService;
        this.logger = logger;
    }
    async login(loginDto) {
        try {
            this.logger.info('Attempting login', { email: loginDto.email });
            const response = await axios_1.default.post(`${constants_1.NEXQLOUD_AUTH_BACKEND_URL}/users/login`, {
                email: loginDto.email,
                password: loginDto.password,
                assignableRoleNames: ['internal_dashboard_user'],
            });
            this.logger.info('Login successful', { email: loginDto.email });
            return {
                user: response.data.user,
                accessToken: response.data.accessToken,
                refreshToken: response.data.refreshToken,
            };
        }
        catch (error) {
            this.logger.error('Login failed', {
                email: loginDto.email,
                error: error.response?.data || error.message
            });
            if (error.response?.status === 401) {
                throw new common_1.UnauthorizedException('Invalid credentials');
            }
            throw new common_1.BadRequestException(error.response?.data?.message || 'Login failed');
        }
    }
    async signup(signupDto) {
        try {
            this.logger.info('Attempting signup', { email: signupDto.email });
            const otpResponse = await axios_1.default.post(`${constants_1.NEXQLOUD_AUTH_BACKEND_URL}/users/email-onboarding/send-otp`, {
                email: signupDto.email,
                accountName: 'Internal Dashboard',
            });
            this.logger.info('OTP sent for signup', { email: signupDto.email });
            return {
                sessionId: otpResponse.data.sessionId,
            };
        }
        catch (error) {
            this.logger.error('Signup failed', {
                email: signupDto.email,
                error: error.response?.data || error.message
            });
            if (error.response?.status === 409) {
                throw new common_1.ConflictException('User already exists');
            }
            throw new common_1.BadRequestException(error.response?.data?.message || 'Signup failed');
        }
    }
    async verifyOtpAndCreateUser(sessionId, otp, userDetails) {
        try {
            this.logger.info('Verifying OTP and creating user', { sessionId });
            await axios_1.default.post(`${constants_1.NEXQLOUD_AUTH_BACKEND_URL}/users/email-onboarding/verify-otp`, {
                otp,
            }, {
                headers: {
                    'Session': sessionId,
                },
            });
            const createUserResponse = await axios_1.default.post(`${constants_1.NEXQLOUD_AUTH_BACKEND_URL}/users/email-onboarding/create-user`, {
                firstName: userDetails.firstName,
                lastName: userDetails.lastName,
                password: userDetails.password,
                role: ['internal_dashboard_user'],
            }, {
                headers: {
                    'Session': sessionId,
                },
            });
            this.logger.info('User created successfully', { sessionId });
            return {
                user: createUserResponse.data.user,
                accessToken: createUserResponse.data.accessToken,
                refreshToken: createUserResponse.data.refreshToken,
            };
        }
        catch (error) {
            this.logger.error('OTP verification or user creation failed', {
                sessionId,
                error: error.response?.data || error.message
            });
            if (error.response?.status === 401) {
                throw new common_1.UnauthorizedException('Invalid or expired OTP');
            }
            throw new common_1.BadRequestException(error.response?.data?.message || 'User creation failed');
        }
    }
    async refreshToken(refreshToken) {
        try {
            this.logger.info('Attempting token refresh');
            const response = await axios_1.default.post(`${constants_1.NEXQLOUD_AUTH_BACKEND_URL}/auth/refresh`, {
                refreshToken,
            });
            this.logger.info('Token refresh successful');
            return {
                accessToken: response.data.accessToken,
            };
        }
        catch (error) {
            this.logger.error('Token refresh failed', {
                error: error.response?.data || error.message
            });
            throw new common_1.UnauthorizedException('Token refresh failed');
        }
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        logging_service_1.LoggingService])
], AuthService);
//# sourceMappingURL=auth.service.js.map