"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.refreshTokenSchema = exports.verifyOtpSchema = exports.signupSchema = exports.loginSchema = void 0;
const Joi = require("joi");
exports.loginSchema = Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().required(),
});
exports.signupSchema = Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().min(6).required(),
    firstName: Joi.string().optional(),
    lastName: Joi.string().optional(),
});
exports.verifyOtpSchema = Joi.object({
    otp: Joi.string().length(6).required(),
    password: Joi.string().min(6).required(),
    firstName: Joi.string().optional(),
    lastName: Joi.string().optional(),
});
exports.refreshTokenSchema = Joi.object({
    refreshToken: Joi.string().required(),
});
//# sourceMappingURL=auth.validation.js.map