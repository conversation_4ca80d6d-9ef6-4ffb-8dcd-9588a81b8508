import { JwtService } from '@nestjs/jwt';
import { LoggingService } from '../logging/logging.service';
export interface LoginDto {
    email: string;
    password: string;
}
export interface SignupDto {
    email: string;
    password: string;
    firstName?: string;
    lastName?: string;
}
export interface AuthResponse {
    user: any;
    accessToken: string;
    refreshToken: string;
}
export declare class AuthService {
    private readonly jwtService;
    private readonly logger;
    constructor(jwtService: JwtService, logger: LoggingService);
    login(loginDto: LoginDto): Promise<AuthResponse>;
    signup(signupDto: SignupDto): Promise<{
        sessionId: string;
    }>;
    verifyOtpAndCreateUser(sessionId: string, otp: string, userDetails: {
        firstName?: string;
        lastName?: string;
        password: string;
    }): Promise<AuthResponse>;
    refreshToken(refreshToken: string): Promise<{
        accessToken: string;
    }>;
    logout(authorization?: string): Promise<{
        message: string;
    }>;
}
