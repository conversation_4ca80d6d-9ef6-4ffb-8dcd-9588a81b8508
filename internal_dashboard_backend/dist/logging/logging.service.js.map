{"version": 3, "file": "logging.service.js", "sourceRoot": "", "sources": ["../../src/logging/logging.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,mCAAmC;AACnC,6DAA6D;AAGtD,IAAM,cAAc,GAApB,MAAM,cAAc;IAGzB;QACE,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC;YACjC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO;YAC/D,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,OAAO,CAC5B,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE,EAC1B,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EACtC,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,CACtB;YACD,WAAW,EAAE,EAAE,OAAO,EAAE,4BAA4B,EAAE;YACtD,UAAU,EAAE;gBACV,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC;oBAC7B,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,OAAO,CAC5B,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,EACzB,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,CACxB;iBACF,CAAC;gBACF,IAAI,eAAe,CAAC;oBAClB,QAAQ,EAAE,qBAAqB;oBAC/B,WAAW,EAAE,YAAY;oBACzB,OAAO,EAAE,KAAK;oBACd,QAAQ,EAAE,KAAK;iBAChB,CAAC;gBACF,IAAI,eAAe,CAAC;oBAClB,QAAQ,EAAE,kBAAkB;oBAC5B,WAAW,EAAE,YAAY;oBACzB,KAAK,EAAE,OAAO;oBACd,OAAO,EAAE,KAAK;oBACd,QAAQ,EAAE,KAAK;iBAChB,CAAC;aACH;SACF,CAAC,CAAC;IACL,CAAC;IAED,IAAI,CAAC,OAAe,EAAE,IAAU;QAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,KAAK,CAAC,OAAe,EAAE,IAAU;QAC/B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACnC,CAAC;IAED,IAAI,CAAC,OAAe,EAAE,IAAU;QAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,KAAK,CAAC,OAAe,EAAE,IAAU;QAC/B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACnC,CAAC;IAED,OAAO,CAAC,OAAe,EAAE,IAAU;QACjC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACrC,CAAC;CACF,CAAA;AAvDY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;;GACA,cAAc,CAuD1B"}