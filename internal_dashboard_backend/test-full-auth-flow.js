const axios = require('axios');

const INTERNAL_DASHBOARD_API = 'http://localhost:3006';

async function testFullAuthFlow() {
  console.log('🔍 Testing Full Authentication Flow\n');

  try {
    // Step 1: Create a test user account
    console.log('1. Creating test user account...');
    const testEmail = `full-auth-test-${Date.now()}@example.com`;
    
    const signupResponse = await axios.post(`${INTERNAL_DASHBOARD_API}/auth/signup`, {
      email: testEmail,
      password: 'password123',
      firstName: 'Full',
      lastName: 'Auth'
    });
    
    const sessionId = signupResponse.data.sessionId;
    console.log('✅ Signup successful');
    console.log('   Email:', testEmail);
    console.log('   Session ID:', sessionId);
    console.log('');

    console.log('2. 📧 OTP Verification Instructions:');
    console.log('');
    console.log('To complete the test, you need to:');
    console.log('');
    console.log('a) Check your email for the 6-digit OTP');
    console.log('b) Use this curl command to verify OTP:');
    console.log('');
    console.log(`curl -X POST http://localhost:3006/auth/verify-otp \\`);
    console.log(`  -H "Content-Type: application/json" \\`);
    console.log(`  -H "Session: ${sessionId}" \\`);
    console.log(`  -d '{"otp": "YOUR_OTP_HERE", "password": "password123", "firstName": "Full", "lastName": "Auth"}'`);
    console.log('');
    console.log('c) After OTP verification, test login with:');
    console.log('');
    console.log(`curl -X POST http://localhost:3006/auth/login \\`);
    console.log(`  -H "Content-Type: application/json" \\`);
    console.log(`  -d '{"email": "${testEmail}", "password": "password123"}'`);
    console.log('');

    console.log('3. 🔧 Backend Fix Applied:');
    console.log('');
    console.log('✅ Enhanced login method to handle NexQloud Auth Backend response');
    console.log('✅ Added support for sessionToken-based authentication');
    console.log('✅ Added fallback user object creation');
    console.log('✅ Added user info retrieval with session token');
    console.log('✅ Improved error handling and logging');
    console.log('');

    console.log('4. 🎯 Expected Flow:');
    console.log('');
    console.log('NexQloud Auth Backend Response:');
    console.log('{');
    console.log('  "sessionToken": "jwt-token-here",');
    console.log('  "requires2FA": false,');
    console.log('  "loginComplete": true/false');
    console.log('}');
    console.log('');
    console.log('Internal Dashboard Backend Response:');
    console.log('{');
    console.log('  "user": { "id": 123, "email": "<EMAIL>", ... },');
    console.log('  "accessToken": "session-token-or-jwt",');
    console.log('  "refreshToken": "session-token-or-jwt"');
    console.log('}');
    console.log('');

    console.log('5. 🚀 Testing Steps:');
    console.log('');
    console.log('Step 1: Complete OTP verification (check email)');
    console.log('Step 2: Test login with the new account');
    console.log('Step 3: Check backend logs for response structure');
    console.log('Step 4: Verify localStorage gets proper values');
    console.log('Step 5: Test frontend login flow');
    console.log('');

    console.log('6. 🎉 Expected Results:');
    console.log('');
    console.log('✅ Login returns proper user object');
    console.log('✅ accessToken contains valid JWT or session token');
    console.log('✅ refreshToken available for token refresh');
    console.log('✅ Frontend localStorage contains valid data');
    console.log('✅ Dashboard loads with user information');
    console.log('');

    console.log('🔧 Ready for testing! Complete the OTP verification and try login.');

  } catch (error) {
    console.error('❌ Test setup failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testFullAuthFlow();
