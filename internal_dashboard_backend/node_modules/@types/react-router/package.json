{"name": "@types/react-router", "version": "5.1.20", "description": "TypeScript definitions for React Router", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pawfa", "githubUsername": "pawfa"}], "main": "", "types": "index.d.ts", "typesVersions": {"<=4.6": {"*": ["ts4.6/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "^4.7.11", "@types/react": "*"}, "typesPublisherContentHash": "471509be13705fc944e92092c64b94ac19712efd46cd3b0bfe38faefb539955f", "typeScriptVersion": "4.2"}