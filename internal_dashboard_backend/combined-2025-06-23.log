{"email":"<EMAIL>","level":"info","message":"Attempting signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:00:26.083Z"}
{"email":"<EMAIL>","level":"info","message":"OTP sent for signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:00:27.896Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:00:53.863Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Lo<PERSON> failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:00:54.121Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:00:54.122Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:02:09.234Z"}
{"email":"<EMAIL>","level":"info","message":"OTP sent for signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:02:10.902Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:02:10.905Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:02:11.146Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:02:11.146Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:02:11.153Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:02:11.376Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:02:11.377Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:29:09.350Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:29:10.731Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:29:10.731Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:29:13.276Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:29:13.831Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:29:13.832Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:29:50.260Z"}
{"email":"<EMAIL>","level":"info","message":"OTP sent for signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:29:52.119Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:29:52.122Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:29:52.342Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:29:52.342Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:29:52.346Z"}
{"email":"<EMAIL>","level":"info","message":"OTP sent for signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:29:53.429Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:36:53.426Z"}
{"email":"<EMAIL>","level":"info","message":"OTP sent for signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:36:55.080Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:36:55.083Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:36:55.303Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:36:55.303Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:36:55.307Z"}
{"email":"<EMAIL>","level":"info","message":"OTP sent for signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:36:56.315Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:37:16.152Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:37:16.373Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:37:16.375Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:37:17.424Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:37:17.651Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:37:17.652Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:37:24.384Z"}
{"email":"<EMAIL>","level":"info","message":"OTP sent for signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:37:26.041Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:39:45.669Z"}
{"email":"<EMAIL>","level":"info","message":"OTP sent for signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:39:47.412Z"}
{"level":"info","message":"Verifying OTP and creating user","service":"internal-dashboard-backend","sessionId":"fb6e0c055c702f640b19902d90e698d408d4711b18da84b99e4f33fe01e9599d","timestamp":"2025-06-23T01:41:33.635Z"}
{"error":{"error":"BAD_REQUEST","error_code":"unknown_error","message":"Invalid user data","statusCode":400},"level":"error","message":"OTP verification or user creation failed","service":"internal-dashboard-backend","sessionId":"fb6e0c055c702f640b19902d90e698d408d4711b18da84b99e4f33fe01e9599d","timestamp":"2025-06-23T01:41:35.051Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/verify-otp","service":"internal-dashboard-backend","stack":"BadRequestException: Invalid user data\n    at AuthService.verifyOtpAndCreateUser (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:139:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:41:35.051Z"}
{"level":"info","message":"Verifying OTP and creating user","service":"internal-dashboard-backend","sessionId":"fb6e0c055c702f640b19902d90e698d408d4711b18da84b99e4f33fe01e9599d","timestamp":"2025-06-23T01:41:47.546Z"}
{"error":{"error":"UNAUTHORIZED","error_code":"invalid_session","message":"SessionException","statusCode":401},"level":"error","message":"OTP verification or user creation failed","service":"internal-dashboard-backend","sessionId":"fb6e0c055c702f640b19902d90e698d408d4711b18da84b99e4f33fe01e9599d","timestamp":"2025-06-23T01:41:47.789Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/verify-otp","service":"internal-dashboard-backend","stack":"UnauthorizedException: Invalid or expired OTP\n    at AuthService.verifyOtpAndCreateUser (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:136:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":401,"timestamp":"2025-06-23T01:41:47.791Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:44:19.163Z"}
{"email":"<EMAIL>","level":"info","message":"OTP sent for signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:44:20.993Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:45:38.808Z"}
{"email":"<EMAIL>","level":"info","message":"OTP sent for signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:45:40.504Z"}
{"level":"info","message":"Verifying OTP and creating user","service":"internal-dashboard-backend","sessionId":"1e75f2c14854f0feaaf8f33cbfa57a443278cef0935329a43964c10e622bbad3","timestamp":"2025-06-23T01:45:40.518Z"}
{"error":{"error":"BAD_REQUEST","error_code":"unknown_error","message":"Invalid OTP.","statusCode":400},"level":"error","message":"OTP verification or user creation failed","service":"internal-dashboard-backend","sessionId":"1e75f2c14854f0feaaf8f33cbfa57a443278cef0935329a43964c10e622bbad3","timestamp":"2025-06-23T01:45:40.756Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/verify-otp","service":"internal-dashboard-backend","stack":"BadRequestException: Invalid OTP.\n    at AuthService.verifyOtpAndCreateUser (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:139:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:45:40.756Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:49:09.898Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:49:10.147Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:49:10.148Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:49:14.577Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:49:14.824Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:49:14.824Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:49:22.006Z"}
{"email":"<EMAIL>","level":"info","message":"OTP sent for signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:49:23.715Z"}
{"level":"info","message":"Verifying OTP and creating user","service":"internal-dashboard-backend","sessionId":"cc3ba551b3aa23ec454bdf9fc48743a5fa5e295a3f01d3d0521fe87951cdf9cc","timestamp":"2025-06-23T01:49:33.196Z"}
{"error":{"error":"BAD_REQUEST","error_code":"unknown_error","message":"Invalid user data","statusCode":400},"level":"error","message":"OTP verification or user creation failed","service":"internal-dashboard-backend","sessionId":"cc3ba551b3aa23ec454bdf9fc48743a5fa5e295a3f01d3d0521fe87951cdf9cc","timestamp":"2025-06-23T01:49:34.179Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/verify-otp","service":"internal-dashboard-backend","stack":"BadRequestException: Invalid user data\n    at AuthService.verifyOtpAndCreateUser (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:139:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:49:34.183Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:50:56.960Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:50:57.221Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:50:57.221Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:51:30.831Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:51:31.073Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:51:31.073Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:52:45.142Z"}
{"email":"<EMAIL>","level":"info","message":"OTP sent for signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:52:46.885Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:53:24.499Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:53:24.781Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:53:24.781Z"}
{"level":"info","message":"Verifying OTP and creating user","service":"internal-dashboard-backend","sessionId":"invalid-session-id","timestamp":"2025-06-23T01:53:24.787Z"}
{"error":{"error":"UNAUTHORIZED","error_code":"invalid_session","message":"SessionException","statusCode":401},"level":"error","message":"OTP verification or user creation failed","service":"internal-dashboard-backend","sessionId":"invalid-session-id","timestamp":"2025-06-23T01:53:25.045Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/verify-otp","service":"internal-dashboard-backend","stack":"UnauthorizedException: Invalid or expired OTP\n    at AuthService.verifyOtpAndCreateUser (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:136:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":401,"timestamp":"2025-06-23T01:53:25.045Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:53:25.049Z"}
{"email":"<EMAIL>","level":"info","message":"OTP sent for signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:53:26.780Z"}
{"level":"info","message":"Verifying OTP and creating user","service":"internal-dashboard-backend","sessionId":"d2669ba29e4606a3b281d3ef205f18a33b895f5438a4294d30608252b7c547b8","timestamp":"2025-06-23T01:53:41.286Z"}
{"error":{"error":"BAD_REQUEST","error_code":"unknown_error","message":"Invalid OTP.","statusCode":400},"level":"error","message":"OTP verification or user creation failed","service":"internal-dashboard-backend","sessionId":"d2669ba29e4606a3b281d3ef205f18a33b895f5438a4294d30608252b7c547b8","timestamp":"2025-06-23T01:53:41.561Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/verify-otp","service":"internal-dashboard-backend","stack":"BadRequestException: Invalid OTP.\n    at AuthService.verifyOtpAndCreateUser (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:139:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:53:41.561Z"}
{"level":"info","message":"Verifying OTP and creating user","service":"internal-dashboard-backend","sessionId":"d2669ba29e4606a3b281d3ef205f18a33b895f5438a4294d30608252b7c547b8","timestamp":"2025-06-23T01:53:43.010Z"}
{"error":{"error":"BAD_REQUEST","error_code":"unknown_error","message":"Invalid OTP.","statusCode":400},"level":"error","message":"OTP verification or user creation failed","service":"internal-dashboard-backend","sessionId":"d2669ba29e4606a3b281d3ef205f18a33b895f5438a4294d30608252b7c547b8","timestamp":"2025-06-23T01:53:43.268Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/verify-otp","service":"internal-dashboard-backend","stack":"BadRequestException: Invalid OTP.\n    at AuthService.verifyOtpAndCreateUser (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:139:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:53:43.269Z"}
{"level":"info","message":"Verifying OTP and creating user","service":"internal-dashboard-backend","sessionId":"d2669ba29e4606a3b281d3ef205f18a33b895f5438a4294d30608252b7c547b8","timestamp":"2025-06-23T01:54:08.415Z"}
{"error":{"error":"BAD_REQUEST","error_code":"unknown_error","message":"Invalid OTP.","statusCode":400},"level":"error","message":"OTP verification or user creation failed","service":"internal-dashboard-backend","sessionId":"d2669ba29e4606a3b281d3ef205f18a33b895f5438a4294d30608252b7c547b8","timestamp":"2025-06-23T01:54:08.685Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/verify-otp","service":"internal-dashboard-backend","stack":"BadRequestException: Invalid OTP.\n    at AuthService.verifyOtpAndCreateUser (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:139:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:54:08.685Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:54:20.678Z"}
{"email":"<EMAIL>","level":"info","message":"OTP sent for signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:54:22.572Z"}
{"level":"info","message":"Verifying OTP and creating user","service":"internal-dashboard-backend","sessionId":"eea8d0eb94b5f9b3321d063a6d8b1b354533d9c2382c189314685247abde7c6f","timestamp":"2025-06-23T01:54:38.780Z"}
{"level":"info","message":"User created successfully","service":"internal-dashboard-backend","sessionId":"eea8d0eb94b5f9b3321d063a6d8b1b354533d9c2382c189314685247abde7c6f","timestamp":"2025-06-23T01:54:40.732Z"}
{"level":"error","message":"Exception caught [object Object]","method":"GET","path":"/favicon.ico","service":"internal-dashboard-backend","stack":"NotFoundException: Cannot GET /favicon.ico\n    at callback (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/routes-resolver.js:77:19)\n    at /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:23\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/index.js:328:13)","status":404,"timestamp":"2025-06-23T01:57:53.079Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/logout","service":"internal-dashboard-backend","stack":"NotFoundException: Cannot POST /logout\n    at callback (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/routes-resolver.js:77:19)\n    at /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:23\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/body-parser/lib/types/urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/index.js:328:13)","status":404,"timestamp":"2025-06-23T05:58:44.420Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T05:58:49.632Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T05:58:52.638Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:00:21.355Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:00:21.906Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:00:24.778Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:00:25.323Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:00:28.975Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:00:29.509Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:00:30.001Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:00:30.610Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:00:30.989Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:00:31.555Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:00:35.071Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:00:35.717Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:00:55.743Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:00:56.291Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:00:57.579Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:00:58.111Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:00:58.812Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:00:58.881Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:00:59.348Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:01:00.543Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:01:01.098Z"}
{"email":"<EMAIL>","level":"info","message":"OTP sent for signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:01:01.946Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:01:02.015Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:01:02.585Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:01:04.843Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:01:05.374Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:01:07.954Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:01:08.495Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:01:40.415Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:01:40.863Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:01:40.948Z"}
{"email":"<EMAIL>","error":{"error":"UNAUTHORIZED","error_code":"unknown_error","message":"Unauthorized","statusCode":401},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:01:41.198Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"UnauthorizedException: Invalid credentials\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:57:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":401,"timestamp":"2025-06-23T06:01:41.199Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:02:18.468Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:02:19.034Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:02:19.807Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:02:20.359Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:02:24.252Z"}
{"email":"<EMAIL>","error":{"error":"CONFLICT","error_code":"unknown_error","message":"Account with this email already exists.","statusCode":409},"level":"error","message":"Signup failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:02:24.481Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/signup","service":"internal-dashboard-backend","stack":"ConflictException: User already exists\n    at AuthService.signup (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:86:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":409,"timestamp":"2025-06-23T06:02:24.481Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:02:33.294Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:02:33.833Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:02:43.641Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:02:44.203Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:02:44.860Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:02:45.410Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:02:46.291Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:02:46.853Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:02:48.297Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:02:48.863Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:02:49.495Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:02:50.090Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:02:50.623Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:02:51.172Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:03:00.858Z"}
{"email":"<EMAIL>","level":"info","message":"OTP sent for signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:03:02.564Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:03:02.571Z"}
{"email":"<EMAIL>","error":{"error":"UNAUTHORIZED","error_code":"unknown_error","message":"Unauthorized","statusCode":401},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:03:02.897Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"UnauthorizedException: Invalid credentials\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:57:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":401,"timestamp":"2025-06-23T06:03:02.898Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:03:17.090Z"}
{"email":"<EMAIL>","error":{"error":"UNAUTHORIZED","error_code":"unknown_error","message":"Unauthorized","statusCode":401},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:03:17.414Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"UnauthorizedException: Invalid credentials\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:57:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":401,"timestamp":"2025-06-23T06:03:17.414Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:04:11.699Z"}
{"email":"<EMAIL>","error":{"error":"UNAUTHORIZED","error_code":"unknown_error","message":"Unauthorized","statusCode":401},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:04:12.090Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"UnauthorizedException: Invalid credentials\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:65:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":401,"timestamp":"2025-06-23T06:04:12.090Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:05:09.500Z"}
{"email":"<EMAIL>","error":{"error":"CONFLICT","error_code":"unknown_error","message":"Account with this email already exists.","statusCode":409},"level":"error","message":"Signup failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:05:09.848Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/signup","service":"internal-dashboard-backend","stack":"ConflictException: User already exists\n    at AuthService.signup (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:94:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":409,"timestamp":"2025-06-23T06:05:09.849Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:05:23.508Z"}
{"email":"<EMAIL>","level":"info","message":"OTP sent for signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:05:25.057Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:05:55.154Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:05:55.773Z"}
{"hasAccessToken":false,"hasRefreshToken":false,"hasUser":false,"level":"info","message":"Login response structure","responseData":{"loginComplete":true,"requires2FA":false,"sessionToken":"665799552615f8aeab9f4fb35c6dc7e7b83e4e8c9bf59b63f59359fbba8963d9"},"service":"internal-dashboard-backend","timestamp":"2025-06-23T06:05:55.773Z"}
{"hasAccessToken":false,"hasRefreshToken":false,"hasUser":false,"level":"error","message":"Missing required fields in login response","responseKeys":["sessionToken","requires2FA","loginComplete"],"service":"internal-dashboard-backend","timestamp":"2025-06-23T06:05:55.774Z"}
{"email":"<EMAIL>","error":"Invalid response from authentication service","level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:05:55.774Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: Login failed\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T06:05:55.777Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:06:20.933Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:06:21.491Z"}
{"hasAccessToken":false,"hasRefreshToken":false,"hasUser":false,"level":"info","message":"Login response structure","responseData":{"loginComplete":true,"requires2FA":false,"sessionToken":"7f05d3b635a086e6567a2cdf7201466680b74599596b54b67ec5fba6a1187f59"},"service":"internal-dashboard-backend","timestamp":"2025-06-23T06:06:21.492Z"}
{"hasAccessToken":false,"hasRefreshToken":false,"hasUser":false,"level":"error","message":"Missing required fields in login response","responseKeys":["sessionToken","requires2FA","loginComplete"],"service":"internal-dashboard-backend","timestamp":"2025-06-23T06:06:21.492Z"}
{"email":"<EMAIL>","error":"Invalid response from authentication service","level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:06:21.492Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: Login failed\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T06:06:21.495Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:06:28.921Z"}
{"email":"<EMAIL>","error":{"error":"CONFLICT","error_code":"unknown_error","message":"Account with this email already exists.","statusCode":409},"level":"error","message":"Signup failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:06:29.182Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/signup","service":"internal-dashboard-backend","stack":"ConflictException: User already exists\n    at AuthService.signup (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:109:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":409,"timestamp":"2025-06-23T06:06:29.182Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:07:21.833Z"}
{"email":"<EMAIL>","error":{"error":"CONFLICT","error_code":"unknown_error","message":"Account with this email already exists.","statusCode":409},"level":"error","message":"Signup failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:07:22.087Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/signup","service":"internal-dashboard-backend","stack":"ConflictException: User already exists\n    at AuthService.signup (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:109:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":409,"timestamp":"2025-06-23T06:07:22.088Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:07:37.002Z"}
{"email":"<EMAIL>","level":"info","message":"Login successful","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:07:37.556Z"}
{"hasAccessToken":false,"hasRefreshToken":false,"hasUser":false,"level":"info","message":"Login response structure","responseData":{"loginComplete":true,"requires2FA":false,"sessionToken":"708661a2bbc02f0274143bfa2fecdfd0275f59b2ed3f04a70c12370f69a596e9"},"service":"internal-dashboard-backend","timestamp":"2025-06-23T06:07:37.557Z"}
{"hasAccessToken":false,"hasRefreshToken":false,"hasUser":false,"level":"error","message":"Missing required fields in login response","responseKeys":["sessionToken","requires2FA","loginComplete"],"service":"internal-dashboard-backend","timestamp":"2025-06-23T06:07:37.557Z"}
{"email":"<EMAIL>","error":"Invalid response from authentication service","level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:07:37.557Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: Login failed\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T06:07:37.557Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:08:59.717Z"}
{"email":"<EMAIL>","level":"info","message":"Initial login response","responseData":{"loginComplete":true,"requires2FA":false,"sessionToken":"96c40af50bb2cb5e943a7baa838b7b600879a36fecffc03247a47cfe592f3700"},"service":"internal-dashboard-backend","timestamp":"2025-06-23T06:09:00.351Z"}
{"hasAccessToken":true,"hasRefreshToken":false,"hasUser":false,"level":"error","loginComplete":true,"message":"Missing required fields in direct login response","responseKeys":["sessionToken","requires2FA","loginComplete"],"service":"internal-dashboard-backend","timestamp":"2025-06-23T06:09:00.351Z"}
{"email":"<EMAIL>","error":"Invalid response from authentication service","level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:09:00.352Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: Login failed\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:118:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T06:09:00.354Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:09:10.882Z"}
{"email":"<EMAIL>","level":"info","message":"Initial login response","responseData":{"loginComplete":true,"requires2FA":false,"sessionToken":"504e695ac217a41718f30b470489ca2738b936509b22e797202fe2b678ba1244"},"service":"internal-dashboard-backend","timestamp":"2025-06-23T06:09:11.556Z"}
{"hasAccessToken":true,"hasRefreshToken":false,"hasUser":false,"level":"error","loginComplete":true,"message":"Missing required fields in direct login response","responseKeys":["sessionToken","requires2FA","loginComplete"],"service":"internal-dashboard-backend","timestamp":"2025-06-23T06:09:11.561Z"}
{"email":"<EMAIL>","error":"Invalid response from authentication service","level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:09:11.562Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: Login failed\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:118:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T06:09:11.562Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:09:15.952Z"}
{"email":"<EMAIL>","level":"info","message":"Initial login response","responseData":{"loginComplete":true,"requires2FA":false,"sessionToken":"7ce1d02b54543dc6171aac4705cd06cd9eb2851d9c7a4d786154f99cc93c4870"},"service":"internal-dashboard-backend","timestamp":"2025-06-23T06:09:16.524Z"}
{"hasAccessToken":true,"hasRefreshToken":false,"hasUser":false,"level":"error","loginComplete":true,"message":"Missing required fields in direct login response","responseKeys":["sessionToken","requires2FA","loginComplete"],"service":"internal-dashboard-backend","timestamp":"2025-06-23T06:09:16.524Z"}
{"email":"<EMAIL>","error":"Invalid response from authentication service","level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:09:16.525Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: Login failed\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:123:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T06:09:16.526Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:09:25.336Z"}
{"email":"<EMAIL>","error":{"error":"UNAUTHORIZED","error_code":"unknown_error","message":"Unauthorized","statusCode":401},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:09:25.670Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"UnauthorizedException: Invalid credentials\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:120:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":401,"timestamp":"2025-06-23T06:09:25.671Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:09:48.132Z"}
{"email":"<EMAIL>","level":"info","message":"Initial login response","responseData":{"loginComplete":true,"requires2FA":false,"sessionToken":"596822c52b4c4851f0c4c985e56d2d97473815e316bdabc5e2a0f7400dbeb63c"},"service":"internal-dashboard-backend","timestamp":"2025-06-23T06:09:48.805Z"}
{"hasAccessToken":true,"hasRefreshToken":false,"hasUser":false,"level":"error","loginComplete":true,"message":"Missing required fields in direct login response","responseKeys":["sessionToken","requires2FA","loginComplete"],"service":"internal-dashboard-backend","timestamp":"2025-06-23T06:09:48.805Z"}
{"email":"<EMAIL>","error":"Invalid response from authentication service","level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:09:48.805Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: Login failed\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:123:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T06:09:48.806Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:10:15.649Z"}
{"email":"<EMAIL>","level":"info","message":"OTP sent for signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:10:17.385Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting login","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:10:25.546Z"}
{"email":"<EMAIL>","level":"info","message":"Initial login response","responseData":{"loginComplete":true,"requires2FA":false,"sessionToken":"5c7961e6208e2c31637ad7d81a76aea7e21dee7d7a21a60fd17aeb8a7f5bda9f"},"service":"internal-dashboard-backend","timestamp":"2025-06-23T06:10:26.174Z"}
{"hasAccessToken":true,"hasRefreshToken":false,"hasUser":false,"level":"error","loginComplete":true,"message":"Missing required fields in direct login response","responseKeys":["sessionToken","requires2FA","loginComplete"],"service":"internal-dashboard-backend","timestamp":"2025-06-23T06:10:26.174Z"}
{"email":"<EMAIL>","error":"Invalid response from authentication service","level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:10:26.174Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: Login failed\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:123:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T06:10:26.174Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:10:33.059Z"}
{"email":"<EMAIL>","error":{"error":"CONFLICT","error_code":"unknown_error","message":"Account with this email already exists.","statusCode":409},"level":"error","message":"Signup failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:10:33.298Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/signup","service":"internal-dashboard-backend","stack":"ConflictException: User already exists\n    at AuthService.signup (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:149:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":409,"timestamp":"2025-06-23T06:10:33.299Z"}
{"email":"<EMAIL>","level":"info","message":"Attempting signup","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:10:45.552Z"}
{"email":"<EMAIL>","error":{"error":"CONFLICT","error_code":"unknown_error","message":"Account with this email already exists.","statusCode":409},"level":"error","message":"Signup failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:10:45.798Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/signup","service":"internal-dashboard-backend","stack":"ConflictException: User already exists\n    at AuthService.signup (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:149:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":409,"timestamp":"2025-06-23T06:10:45.798Z"}
