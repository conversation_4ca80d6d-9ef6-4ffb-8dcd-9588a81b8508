{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:00:54.121Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:00:54.122Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:02:11.146Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:02:11.146Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:02:11.376Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:02:11.377Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:29:10.731Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:29:10.731Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:29:13.831Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:29:13.832Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:29:52.342Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:29:52.342Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:36:55.303Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:36:55.303Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:37:16.373Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:37:16.375Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:37:17.651Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:37:17.652Z"}
{"error":{"error":"BAD_REQUEST","error_code":"unknown_error","message":"Invalid user data","statusCode":400},"level":"error","message":"OTP verification or user creation failed","service":"internal-dashboard-backend","sessionId":"fb6e0c055c702f640b19902d90e698d408d4711b18da84b99e4f33fe01e9599d","timestamp":"2025-06-23T01:41:35.051Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/verify-otp","service":"internal-dashboard-backend","stack":"BadRequestException: Invalid user data\n    at AuthService.verifyOtpAndCreateUser (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:139:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:41:35.051Z"}
{"error":{"error":"UNAUTHORIZED","error_code":"invalid_session","message":"SessionException","statusCode":401},"level":"error","message":"OTP verification or user creation failed","service":"internal-dashboard-backend","sessionId":"fb6e0c055c702f640b19902d90e698d408d4711b18da84b99e4f33fe01e9599d","timestamp":"2025-06-23T01:41:47.789Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/verify-otp","service":"internal-dashboard-backend","stack":"UnauthorizedException: Invalid or expired OTP\n    at AuthService.verifyOtpAndCreateUser (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:136:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":401,"timestamp":"2025-06-23T01:41:47.791Z"}
{"error":{"error":"BAD_REQUEST","error_code":"unknown_error","message":"Invalid OTP.","statusCode":400},"level":"error","message":"OTP verification or user creation failed","service":"internal-dashboard-backend","sessionId":"1e75f2c14854f0feaaf8f33cbfa57a443278cef0935329a43964c10e622bbad3","timestamp":"2025-06-23T01:45:40.756Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/verify-otp","service":"internal-dashboard-backend","stack":"BadRequestException: Invalid OTP.\n    at AuthService.verifyOtpAndCreateUser (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:139:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:45:40.756Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:49:10.147Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:49:10.148Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:49:14.824Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:49:14.824Z"}
{"error":{"error":"BAD_REQUEST","error_code":"unknown_error","message":"Invalid user data","statusCode":400},"level":"error","message":"OTP verification or user creation failed","service":"internal-dashboard-backend","sessionId":"cc3ba551b3aa23ec454bdf9fc48743a5fa5e295a3f01d3d0521fe87951cdf9cc","timestamp":"2025-06-23T01:49:34.179Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/verify-otp","service":"internal-dashboard-backend","stack":"BadRequestException: Invalid user data\n    at AuthService.verifyOtpAndCreateUser (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:139:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:49:34.183Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:50:57.221Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:50:57.221Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:51:31.073Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:51:31.073Z"}
{"email":"<EMAIL>","error":{"error":"NOT_FOUND","error_code":"unknown_error","message":"User not found","statusCode":404},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T01:53:24.781Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: User not found\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:60:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:53:24.781Z"}
{"error":{"error":"UNAUTHORIZED","error_code":"invalid_session","message":"SessionException","statusCode":401},"level":"error","message":"OTP verification or user creation failed","service":"internal-dashboard-backend","sessionId":"invalid-session-id","timestamp":"2025-06-23T01:53:25.045Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/verify-otp","service":"internal-dashboard-backend","stack":"UnauthorizedException: Invalid or expired OTP\n    at AuthService.verifyOtpAndCreateUser (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:136:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":401,"timestamp":"2025-06-23T01:53:25.045Z"}
{"error":{"error":"BAD_REQUEST","error_code":"unknown_error","message":"Invalid OTP.","statusCode":400},"level":"error","message":"OTP verification or user creation failed","service":"internal-dashboard-backend","sessionId":"d2669ba29e4606a3b281d3ef205f18a33b895f5438a4294d30608252b7c547b8","timestamp":"2025-06-23T01:53:41.561Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/verify-otp","service":"internal-dashboard-backend","stack":"BadRequestException: Invalid OTP.\n    at AuthService.verifyOtpAndCreateUser (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:139:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:53:41.561Z"}
{"error":{"error":"BAD_REQUEST","error_code":"unknown_error","message":"Invalid OTP.","statusCode":400},"level":"error","message":"OTP verification or user creation failed","service":"internal-dashboard-backend","sessionId":"d2669ba29e4606a3b281d3ef205f18a33b895f5438a4294d30608252b7c547b8","timestamp":"2025-06-23T01:53:43.268Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/verify-otp","service":"internal-dashboard-backend","stack":"BadRequestException: Invalid OTP.\n    at AuthService.verifyOtpAndCreateUser (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:139:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:53:43.269Z"}
{"error":{"error":"BAD_REQUEST","error_code":"unknown_error","message":"Invalid OTP.","statusCode":400},"level":"error","message":"OTP verification or user creation failed","service":"internal-dashboard-backend","sessionId":"d2669ba29e4606a3b281d3ef205f18a33b895f5438a4294d30608252b7c547b8","timestamp":"2025-06-23T01:54:08.685Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/verify-otp","service":"internal-dashboard-backend","stack":"BadRequestException: Invalid OTP.\n    at AuthService.verifyOtpAndCreateUser (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:139:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T01:54:08.685Z"}
{"level":"error","message":"Exception caught [object Object]","method":"GET","path":"/favicon.ico","service":"internal-dashboard-backend","stack":"NotFoundException: Cannot GET /favicon.ico\n    at callback (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/routes-resolver.js:77:19)\n    at /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:23\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/body-parser/lib/types/urlencoded.js:94:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/index.js:328:13)","status":404,"timestamp":"2025-06-23T01:57:53.079Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/logout","service":"internal-dashboard-backend","stack":"NotFoundException: Cannot POST /logout\n    at callback (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/routes-resolver.js:77:19)\n    at /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:23\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/body-parser/lib/types/urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/index.js:328:13)","status":404,"timestamp":"2025-06-23T05:58:44.420Z"}
{"email":"<EMAIL>","error":{"error":"UNAUTHORIZED","error_code":"unknown_error","message":"Unauthorized","statusCode":401},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:01:41.198Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"UnauthorizedException: Invalid credentials\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:57:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":401,"timestamp":"2025-06-23T06:01:41.199Z"}
{"email":"<EMAIL>","error":{"error":"CONFLICT","error_code":"unknown_error","message":"Account with this email already exists.","statusCode":409},"level":"error","message":"Signup failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:02:24.481Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/signup","service":"internal-dashboard-backend","stack":"ConflictException: User already exists\n    at AuthService.signup (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:86:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":409,"timestamp":"2025-06-23T06:02:24.481Z"}
{"email":"<EMAIL>","error":{"error":"UNAUTHORIZED","error_code":"unknown_error","message":"Unauthorized","statusCode":401},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:03:02.897Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"UnauthorizedException: Invalid credentials\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:57:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":401,"timestamp":"2025-06-23T06:03:02.898Z"}
{"email":"<EMAIL>","error":{"error":"UNAUTHORIZED","error_code":"unknown_error","message":"Unauthorized","statusCode":401},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:03:17.414Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"UnauthorizedException: Invalid credentials\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:57:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":401,"timestamp":"2025-06-23T06:03:17.414Z"}
{"email":"<EMAIL>","error":{"error":"UNAUTHORIZED","error_code":"unknown_error","message":"Unauthorized","statusCode":401},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:04:12.090Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"UnauthorizedException: Invalid credentials\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:65:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":401,"timestamp":"2025-06-23T06:04:12.090Z"}
{"email":"<EMAIL>","error":{"error":"CONFLICT","error_code":"unknown_error","message":"Account with this email already exists.","statusCode":409},"level":"error","message":"Signup failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:05:09.848Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/signup","service":"internal-dashboard-backend","stack":"ConflictException: User already exists\n    at AuthService.signup (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:94:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":409,"timestamp":"2025-06-23T06:05:09.849Z"}
{"hasAccessToken":false,"hasRefreshToken":false,"hasUser":false,"level":"error","message":"Missing required fields in login response","responseKeys":["sessionToken","requires2FA","loginComplete"],"service":"internal-dashboard-backend","timestamp":"2025-06-23T06:05:55.774Z"}
{"email":"<EMAIL>","error":"Invalid response from authentication service","level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:05:55.774Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: Login failed\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T06:05:55.777Z"}
{"hasAccessToken":false,"hasRefreshToken":false,"hasUser":false,"level":"error","message":"Missing required fields in login response","responseKeys":["sessionToken","requires2FA","loginComplete"],"service":"internal-dashboard-backend","timestamp":"2025-06-23T06:06:21.492Z"}
{"email":"<EMAIL>","error":"Invalid response from authentication service","level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:06:21.492Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: Login failed\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T06:06:21.495Z"}
{"email":"<EMAIL>","error":{"error":"CONFLICT","error_code":"unknown_error","message":"Account with this email already exists.","statusCode":409},"level":"error","message":"Signup failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:06:29.182Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/signup","service":"internal-dashboard-backend","stack":"ConflictException: User already exists\n    at AuthService.signup (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:109:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":409,"timestamp":"2025-06-23T06:06:29.182Z"}
{"email":"<EMAIL>","error":{"error":"CONFLICT","error_code":"unknown_error","message":"Account with this email already exists.","statusCode":409},"level":"error","message":"Signup failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:07:22.087Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/signup","service":"internal-dashboard-backend","stack":"ConflictException: User already exists\n    at AuthService.signup (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:109:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":409,"timestamp":"2025-06-23T06:07:22.088Z"}
{"hasAccessToken":false,"hasRefreshToken":false,"hasUser":false,"level":"error","message":"Missing required fields in login response","responseKeys":["sessionToken","requires2FA","loginComplete"],"service":"internal-dashboard-backend","timestamp":"2025-06-23T06:07:37.557Z"}
{"email":"<EMAIL>","error":"Invalid response from authentication service","level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:07:37.557Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: Login failed\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T06:07:37.557Z"}
{"hasAccessToken":true,"hasRefreshToken":false,"hasUser":false,"level":"error","loginComplete":true,"message":"Missing required fields in direct login response","responseKeys":["sessionToken","requires2FA","loginComplete"],"service":"internal-dashboard-backend","timestamp":"2025-06-23T06:09:00.351Z"}
{"email":"<EMAIL>","error":"Invalid response from authentication service","level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:09:00.352Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: Login failed\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:118:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T06:09:00.354Z"}
{"hasAccessToken":true,"hasRefreshToken":false,"hasUser":false,"level":"error","loginComplete":true,"message":"Missing required fields in direct login response","responseKeys":["sessionToken","requires2FA","loginComplete"],"service":"internal-dashboard-backend","timestamp":"2025-06-23T06:09:11.561Z"}
{"email":"<EMAIL>","error":"Invalid response from authentication service","level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:09:11.562Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: Login failed\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:118:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T06:09:11.562Z"}
{"hasAccessToken":true,"hasRefreshToken":false,"hasUser":false,"level":"error","loginComplete":true,"message":"Missing required fields in direct login response","responseKeys":["sessionToken","requires2FA","loginComplete"],"service":"internal-dashboard-backend","timestamp":"2025-06-23T06:09:16.524Z"}
{"email":"<EMAIL>","error":"Invalid response from authentication service","level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:09:16.525Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: Login failed\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:123:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T06:09:16.526Z"}
{"email":"<EMAIL>","error":{"error":"UNAUTHORIZED","error_code":"unknown_error","message":"Unauthorized","statusCode":401},"level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:09:25.670Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"UnauthorizedException: Invalid credentials\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:120:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":401,"timestamp":"2025-06-23T06:09:25.671Z"}
{"hasAccessToken":true,"hasRefreshToken":false,"hasUser":false,"level":"error","loginComplete":true,"message":"Missing required fields in direct login response","responseKeys":["sessionToken","requires2FA","loginComplete"],"service":"internal-dashboard-backend","timestamp":"2025-06-23T06:09:48.805Z"}
{"email":"<EMAIL>","error":"Invalid response from authentication service","level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:09:48.805Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: Login failed\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:123:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T06:09:48.806Z"}
{"hasAccessToken":true,"hasRefreshToken":false,"hasUser":false,"level":"error","loginComplete":true,"message":"Missing required fields in direct login response","responseKeys":["sessionToken","requires2FA","loginComplete"],"service":"internal-dashboard-backend","timestamp":"2025-06-23T06:10:26.174Z"}
{"email":"<EMAIL>","error":"Invalid response from authentication service","level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:10:26.174Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: Login failed\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:123:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T06:10:26.174Z"}
{"email":"<EMAIL>","error":{"error":"CONFLICT","error_code":"unknown_error","message":"Account with this email already exists.","statusCode":409},"level":"error","message":"Signup failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:10:33.298Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/signup","service":"internal-dashboard-backend","stack":"ConflictException: User already exists\n    at AuthService.signup (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:149:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":409,"timestamp":"2025-06-23T06:10:33.299Z"}
{"email":"<EMAIL>","error":{"error":"CONFLICT","error_code":"unknown_error","message":"Account with this email already exists.","statusCode":409},"level":"error","message":"Signup failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:10:45.798Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/signup","service":"internal-dashboard-backend","stack":"ConflictException: User already exists\n    at AuthService.signup (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:149:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":409,"timestamp":"2025-06-23T06:10:45.798Z"}
{"email":"<EMAIL>","error":{"error":"BAD_REQUEST","error_code":"unknown_error","message":["accountName must be longer than or equal to 1 characters","accountName should not be empty"],"statusCode":400},"level":"error","message":"Signup failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:11:57.715Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/signup","service":"internal-dashboard-backend","stack":"BadRequestException: Bad Request Exception\n    at AuthService.signup (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:151:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T06:11:57.716Z"}
{"email":"<EMAIL>","error":"","level":"error","message":"Signup failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:16:29.914Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/signup","service":"internal-dashboard-backend","stack":"BadRequestException: Signup failed\n    at AuthService.signup (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:151:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T06:16:29.914Z"}
{"email":"<EMAIL>","error":"","level":"error","message":"Signup failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T06:22:34.714Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/signup","service":"internal-dashboard-backend","stack":"BadRequestException: Signup failed\n    at AuthService.signup (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:151:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T06:22:34.715Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/logout","service":"internal-dashboard-backend","stack":"NotFoundException: Cannot POST /logout\n    at callback (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/routes-resolver.js:77:19)\n    at /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:23\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/index.js:280:10)\n    at urlencodedParser (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/body-parser/lib/types/urlencoded.js:85:7)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/express/lib/router/index.js:328:13)","status":404,"timestamp":"2025-06-23T09:52:14.911Z"}
{"hasAccessToken":true,"hasRefreshToken":false,"hasUser":false,"level":"error","loginComplete":true,"message":"Missing required fields in direct login response","responseKeys":["sessionToken","requires2FA","loginComplete"],"service":"internal-dashboard-backend","timestamp":"2025-06-23T09:52:19.666Z"}
{"email":"<EMAIL>","error":"Invalid response from authentication service","level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T09:52:19.667Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: Login failed\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:123:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T09:52:19.667Z"}
{"hasAccessToken":true,"hasRefreshToken":false,"hasUser":false,"level":"error","loginComplete":true,"message":"Missing required fields in direct login response","responseKeys":["sessionToken","requires2FA","loginComplete"],"service":"internal-dashboard-backend","timestamp":"2025-06-23T09:52:25.452Z"}
{"email":"<EMAIL>","error":"Invalid response from authentication service","level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T09:52:25.452Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: Login failed\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:123:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T09:52:25.453Z"}
{"hasAccessToken":true,"hasRefreshToken":false,"hasUser":false,"level":"error","loginComplete":true,"message":"Missing required fields in direct login response","responseKeys":["sessionToken","requires2FA","loginComplete"],"service":"internal-dashboard-backend","timestamp":"2025-06-23T09:52:27.045Z"}
{"email":"<EMAIL>","error":"Invalid response from authentication service","level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T09:52:27.045Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: Login failed\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:123:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T09:52:27.045Z"}
{"hasAccessToken":true,"hasRefreshToken":false,"hasUser":false,"level":"error","loginComplete":true,"message":"Missing required fields in direct login response","responseKeys":["sessionToken","requires2FA","loginComplete"],"service":"internal-dashboard-backend","timestamp":"2025-06-23T09:52:35.313Z"}
{"email":"<EMAIL>","error":"Invalid response from authentication service","level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T09:52:35.313Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: Login failed\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:123:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T09:52:35.314Z"}
{"hasAccessToken":true,"hasRefreshToken":false,"hasUser":false,"level":"error","loginComplete":true,"message":"Missing required fields in direct login response","responseKeys":["sessionToken","requires2FA","loginComplete"],"service":"internal-dashboard-backend","timestamp":"2025-06-23T09:53:53.121Z"}
{"email":"<EMAIL>","error":"Invalid response from authentication service","level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T09:53:53.122Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: Login failed\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:123:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T09:53:53.122Z"}
{"hasAccessToken":true,"hasRefreshToken":false,"hasUser":false,"level":"error","loginComplete":true,"message":"Missing required fields in direct login response","responseKeys":["sessionToken","requires2FA","loginComplete"],"service":"internal-dashboard-backend","timestamp":"2025-06-23T09:54:06.400Z"}
{"email":"<EMAIL>","error":"Invalid response from authentication service","level":"error","message":"Login failed","service":"internal-dashboard-backend","timestamp":"2025-06-23T09:54:06.401Z"}
{"level":"error","message":"Exception caught [object Object]","method":"POST","path":"/auth/login","service":"internal-dashboard-backend","stack":"BadRequestException: Login failed\n    at AuthService.login (/Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/src/auth/auth.service.ts:123:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-execution-context.js:46:28\n    at async /Users/<USER>/Desktop/nexqloud/auth/internal_dashboard_backend/node_modules/@nestjs/core/router/router-proxy.js:9:17","status":400,"timestamp":"2025-06-23T09:54:06.402Z"}
