const axios = require('axios');

const INTERNAL_DASHBOARD_API = 'http://localhost:3006';

async function testCompleteFlow() {
  console.log('🔍 Testing Complete Authentication Flow\n');

  try {
    // Step 1: Create a test user account
    console.log('1. Creating test user account...');
    const testEmail = `complete-flow-test-${Date.now()}@example.com`;
    
    const signupResponse = await axios.post(`${INTERNAL_DASHBOARD_API}/auth/signup`, {
      email: testEmail,
      password: 'password123',
      firstName: 'Complete',
      lastName: 'Flow'
    });
    
    const sessionId = signupResponse.data.sessionId;
    console.log('✅ Signup successful, sessionId:', sessionId);
    console.log('');

    console.log('2. 📧 Manual OTP Verification Required:');
    console.log('   - Check your email for the 6-digit OTP');
    console.log('   - Use the frontend at http://localhost:5173 to verify OTP');
    console.log('   - Or use this curl command with the actual OTP:');
    console.log('');
    console.log(`   curl -X POST http://localhost:3006/auth/verify-otp \\`);
    console.log(`     -H "Content-Type: application/json" \\`);
    console.log(`     -H "Session: ${sessionId}" \\`);
    console.log(`     -d '{"otp": "YOUR_OTP_HERE", "password": "password123", "firstName": "Complete", "lastName": "Flow"}'`);
    console.log('');

    console.log('3. 🔧 Debugging localStorage Issue:');
    console.log('');
    console.log('The issue is likely one of these:');
    console.log('');
    console.log('A) Backend Response Structure Mismatch:');
    console.log('   - Frontend expects: { user, accessToken, refreshToken }');
    console.log('   - Backend might return: { user, token, refreshToken } or different structure');
    console.log('');
    console.log('B) NexQloud Auth Backend Response:');
    console.log('   - The response from nexqloud_auth_backend might have different field names');
    console.log('   - Fields might be nested differently');
    console.log('');
    console.log('C) Undefined Values:');
    console.log('   - response.data.user might be undefined');
    console.log('   - response.data.accessToken might be undefined');
    console.log('   - response.data.refreshToken might be undefined');
    console.log('');

    console.log('4. 🎯 Quick Fix Options:');
    console.log('');
    console.log('Option 1: Check nexqloud_auth_backend response structure');
    console.log('Option 2: Add null checks in internal_dashboard_backend');
    console.log('Option 3: Map response fields correctly');
    console.log('Option 4: Add default values for undefined fields');
    console.log('');

    console.log('5. 🧪 Testing Steps:');
    console.log('');
    console.log('a) Complete OTP verification for this account:');
    console.log(`   Email: ${testEmail}`);
    console.log(`   Session: ${sessionId}`);
    console.log('');
    console.log('b) Try logging in with the frontend');
    console.log('c) Check browser localStorage after login');
    console.log('d) Check backend logs for response structure');
    console.log('');

    console.log('6. 🔧 Backend Logging Added:');
    console.log('✅ Added debug logging to auth.service.ts');
    console.log('✅ Will show actual response structure from nexqloud_auth_backend');
    console.log('✅ Will show which fields are present/missing');
    console.log('');

    console.log('🚀 Next: Complete OTP verification and check the logs!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testCompleteFlow();
