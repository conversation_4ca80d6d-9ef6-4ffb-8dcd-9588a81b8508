const axios = require('axios');

const INTERNAL_DASHBOARD_API = 'http://localhost:3006';

async function testLoginResponse() {
  console.log('🔍 Testing Login Response Structure\n');

  try {
    // Step 1: Create a test user account
    console.log('1. Creating test user account...');
    const testEmail = `login-response-test-${Date.now()}@example.com`;
    
    const signupResponse = await axios.post(`${INTERNAL_DASHBOARD_API}/auth/signup`, {
      email: testEmail,
      password: 'password123',
      firstName: 'Login',
      lastName: 'Test'
    });
    
    const sessionId = signupResponse.data.sessionId;
    console.log('✅ Signup successful, sessionId:', sessionId);
    console.log('');

    // Step 2: Complete OTP verification to create the user
    console.log('2. Note: You need to check your email for the OTP to complete account creation');
    console.log('   For testing, let\'s use an existing account instead...');
    console.log('');

    // Step 3: Test login with an existing account (if any)
    console.log('3. Testing login response structure...');
    
    // Try to login with the account that was created earlier
    try {
      const loginResponse = await axios.post(`${INTERNAL_DASHBOARD_API}/auth/login`, {
        email: '<EMAIL>', // Use the account that was successfully created
        password: 'password123'
      });

      console.log('✅ Login successful! Response structure:');
      console.log('Response data:', JSON.stringify(loginResponse.data, null, 2));
      console.log('');

      // Analyze the response structure
      const data = loginResponse.data;
      console.log('🔍 Response Analysis:');
      console.log('- user:', typeof data.user, data.user ? '✅ Present' : '❌ Missing');
      console.log('- accessToken:', typeof data.accessToken, data.accessToken ? '✅ Present' : '❌ Missing');
      console.log('- refreshToken:', typeof data.refreshToken, data.refreshToken ? '✅ Present' : '❌ Missing');
      console.log('- token:', typeof data.token, data.token ? '✅ Present' : '❌ Missing');
      console.log('');

      if (data.user) {
        console.log('👤 User object structure:');
        console.log('- id:', data.user.id ? '✅ Present' : '❌ Missing');
        console.log('- email:', data.user.email ? '✅ Present' : '❌ Missing');
        console.log('- firstName:', data.user.firstName ? '✅ Present' : '❌ Missing');
        console.log('- lastName:', data.user.lastName ? '✅ Present' : '❌ Missing');
        console.log('');
      }

    } catch (loginError) {
      console.log('❌ Login failed (expected if user doesn\'t exist)');
      console.log('Error:', loginError.response?.data || loginError.message);
      console.log('');
      
      console.log('💡 This suggests the user account needs to be created first.');
      console.log('   The OTP verification step is required to complete account creation.');
    }

    console.log('🔧 Frontend Issue Analysis:');
    console.log('The frontend expects this structure:');
    console.log('{');
    console.log('  user: { id, email, firstName, lastName, ... },');
    console.log('  accessToken: "jwt-token-here",');
    console.log('  refreshToken: "refresh-token-here"');
    console.log('}');
    console.log('');

    console.log('🎯 Potential Issues:');
    console.log('1. Backend might be returning undefined values');
    console.log('2. Response structure might not match frontend expectations');
    console.log('3. User account might not be fully created');
    console.log('4. Token generation might be failing');
    console.log('');

    console.log('🚀 Next Steps:');
    console.log('1. Complete the OTP verification for a test account');
    console.log('2. Test login with the verified account');
    console.log('3. Check the actual response structure');
    console.log('4. Fix any mismatches between frontend and backend');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testLoginResponse();
