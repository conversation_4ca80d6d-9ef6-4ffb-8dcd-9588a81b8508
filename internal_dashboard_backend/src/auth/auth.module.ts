import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { LoggingModule } from '../logging/logging.module';
import { TOKEN_SECRET } from '../constants';

@Module({
  imports: [
    JwtModule.register({
      secret: TOKEN_SECRET,
    }),
    LoggingModule,
  ],
  controllers: [AuthController],
  providers: [AuthService],
  exports: [AuthService],
})
export class AuthModule {}
