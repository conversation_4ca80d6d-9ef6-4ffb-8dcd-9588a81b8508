import { Injectable, UnauthorizedException, ConflictException, BadRequestException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import axios from 'axios';
import { LoggingService } from '../logging/logging.service';
import { NEXQLOUD_AUTH_BACKEND_URL } from '../constants';

export interface LoginDto {
  email: string;
  password: string;
}

export interface SignupDto {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
}

export interface AuthResponse {
  user: any;
  accessToken: string;
  refreshToken: string;
}

@Injectable()
export class AuthService {
  constructor(
    private readonly jwtService: JwtService,
    private readonly logger: LoggingService,
  ) {}

  async login(loginDto: LoginDto): Promise<AuthResponse> {
    try {
      this.logger.info('Attempting login', { email: loginDto.email });

      // Call nexqloud_auth_backend login endpoint
      const loginResponse = await axios.post(`${NEXQLOUD_AUTH_BACKEND_URL}/users/login`, {
        email: loginDto.email,
        password: loginDto.password,
        assignableRoleNames: ['internal_dashboard_user'], // Assign internal dashboard role
      });

      this.logger.info('Initial login response', {
        email: loginDto.email,
        responseData: loginResponse.data
      });

      // Check if login requires additional steps
      if (loginResponse.data.requires2FA) {
        throw new BadRequestException('2FA is not supported for internal dashboard');
      }

      // If login is not complete, try to get user info with session token
      if (!loginResponse.data.loginComplete && loginResponse.data.sessionToken) {
        this.logger.info('Login not complete, trying to get user info', { email: loginDto.email });

        try {
          // Try to get user information using the session token
          const userResponse = await axios.get(`${NEXQLOUD_AUTH_BACKEND_URL}/users/me`, {
            headers: {
              'Authorization': `Bearer ${loginResponse.data.sessionToken}`,
            },
          });

          this.logger.info('User info retrieved', {
            hasUser: !!userResponse.data,
            userEmail: userResponse.data?.email
          });

          // Use session token as access token for now
          const user = userResponse.data;
          const accessToken = loginResponse.data.sessionToken;
          const refreshToken = loginResponse.data.sessionToken; // Use same token for refresh

          return { user, accessToken, refreshToken };
        } catch (userError) {
          this.logger.error('Failed to get user info with session token', {
            error: userError.response?.data || userError.message
          });

          // Fallback: create a minimal user object from login email
          const user = {
            email: loginDto.email,
            firstName: 'User',
            lastName: '',
            id: Date.now(), // Temporary ID
          };
          const accessToken = loginResponse.data.sessionToken;
          const refreshToken = loginResponse.data.sessionToken;

          this.logger.info('Using fallback user object', { user });
          return { user, accessToken, refreshToken };
        }
      }

      // If login is already complete, extract the data directly
      const user = loginResponse.data.user;
      const accessToken = loginResponse.data.accessToken || loginResponse.data.token || loginResponse.data.sessionToken;
      const refreshToken = loginResponse.data.refreshToken;

      if (!user || !accessToken || !refreshToken) {
        this.logger.error('Missing required fields in direct login response', {
          hasUser: !!user,
          hasAccessToken: !!accessToken,
          hasRefreshToken: !!refreshToken,
          responseKeys: Object.keys(loginResponse.data),
          loginComplete: loginResponse.data.loginComplete
        });
        throw new BadRequestException('Invalid response from authentication service');
      }

      return { user, accessToken, refreshToken };
    } catch (error) {
      this.logger.error('Login failed', {
        email: loginDto.email,
        error: error.response?.data || error.message
      });

      if (error.response?.status === 401) {
        throw new UnauthorizedException('Invalid credentials');
      }

      throw new BadRequestException(error.response?.data?.message || 'Login failed');
    }
  }

  async signup(signupDto: SignupDto): Promise<{ sessionId: string }> {
    try {
      this.logger.info('Attempting signup', { email: signupDto.email });

      // First, send OTP for email verification
      const otpResponse = await axios.post(`${NEXQLOUD_AUTH_BACKEND_URL}/users/email-onboarding/send-otp`, {
        email: signupDto.email,
      });

      this.logger.info('OTP sent for signup', { email: signupDto.email });

      return {
        sessionId: otpResponse.data.sessionId,
      };
    } catch (error) {
      this.logger.error('Signup failed', {
        email: signupDto.email,
        error: error.response?.data || error.message
      });

      if (error.response?.status === 409) {
        throw new ConflictException('User already exists');
      }

      throw new BadRequestException(error.response?.data?.message || 'Signup failed');
    }
  }

  async verifyOtpAndCreateUser(
    sessionId: string,
    otp: string,
    userDetails: { firstName?: string; lastName?: string; password: string }
  ): Promise<AuthResponse> {
    try {
      this.logger.info('Verifying OTP and creating user', { sessionId });

      // First verify OTP
      await axios.post(`${NEXQLOUD_AUTH_BACKEND_URL}/users/email-onboarding/verify-otp`, {
        otp,
      }, {
        headers: {
          'Session': sessionId,
        },
      });

      // Then create the user
      const createUserResponse = await axios.post(`${NEXQLOUD_AUTH_BACKEND_URL}/users/email-onboarding/create-user`, {
        firstName: userDetails.firstName,
        lastName: userDetails.lastName,
        password: userDetails.password,
        role: ['internal_dashboard_user'], // Assign internal dashboard role
      }, {
        headers: {
          'Session': sessionId,
        },
      });

      this.logger.info('User created successfully', { sessionId });

      // Debug: Log the actual response structure
      this.logger.info('User creation response structure', {
        responseData: createUserResponse.data,
        hasUser: !!createUserResponse.data.user,
        hasAccessToken: !!createUserResponse.data.accessToken,
        hasRefreshToken: !!createUserResponse.data.refreshToken
      });

      // Ensure we have valid data before returning
      const user = createUserResponse.data.user;
      const accessToken = createUserResponse.data.accessToken || createUserResponse.data.token;
      const refreshToken = createUserResponse.data.refreshToken;

      if (!user || !accessToken || !refreshToken) {
        this.logger.error('Missing required fields in user creation response', {
          hasUser: !!user,
          hasAccessToken: !!accessToken,
          hasRefreshToken: !!refreshToken,
          responseKeys: Object.keys(createUserResponse.data)
        });
        throw new BadRequestException('Invalid response from authentication service');
      }

      return {
        user,
        accessToken,
        refreshToken,
      };
    } catch (error) {
      this.logger.error('OTP verification or user creation failed', {
        sessionId,
        error: error.response?.data || error.message
      });

      if (error.response?.status === 401) {
        throw new UnauthorizedException('Invalid or expired OTP');
      }

      throw new BadRequestException(error.response?.data?.message || 'User creation failed');
    }
  }

  async refreshToken(refreshToken: string): Promise<{ accessToken: string }> {
    try {
      this.logger.info('Attempting token refresh');

      const response = await axios.post(`${NEXQLOUD_AUTH_BACKEND_URL}/auth/refresh`, {
        refreshToken,
      });

      this.logger.info('Token refresh successful');

      return {
        accessToken: response.data.accessToken,
      };
    } catch (error) {
      this.logger.error('Token refresh failed', {
        error: error.response?.data || error.message
      });

      throw new UnauthorizedException('Token refresh failed');
    }
  }
}
