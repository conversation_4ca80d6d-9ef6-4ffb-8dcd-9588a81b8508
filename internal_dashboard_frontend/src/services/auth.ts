import type { LoginCredentials, AuthResponse } from '../types';

const API_BASE_URL =
  import.meta.env.VITE_INTERNAL_DASHBOARD_API_URL || 'http://localhost:3006';

class AuthService {
  private static instance: AuthService;
  private token: string | null = null;

  private constructor() {
    // Load token from localStorage on initialization
    const storedToken = localStorage.getItem('auth_token');
    this.token =
      storedToken && storedToken !== 'undefined' && storedToken !== 'null'
        ? storedToken
        : null;

    // If token is invalid, clear all auth data
    if (!this.token) {
      this.clearAuthData();
    }
  }

  private clearAuthData() {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user');
  }

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * Login user with email and password
   */
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      if (!response.ok) {
        let errorMessage = 'Login failed';
        try {
          const errorData = await response.json();
          // Handle nested message structure from NestJS
          if (typeof errorData.message === 'string') {
            errorMessage = errorData.message;
          } else if (
            typeof errorData.message === 'object' &&
            errorData.message.message
          ) {
            errorMessage = errorData.message.message;
          } else if (errorData.error) {
            errorMessage = errorData.error;
          }
        } catch {
          // If response is not JSON, use status text
          errorMessage = response.statusText || errorMessage;
        }
        throw new Error(errorMessage);
      }

      const data: AuthResponse = await response.json();

      // Store token in localStorage and instance
      this.token = data.accessToken;
      localStorage.setItem('auth_token', data.accessToken);
      localStorage.setItem('refresh_token', data.refreshToken);
      localStorage.setItem('user', JSON.stringify(data.user));

      return {
        user: data.user,
        token: data.accessToken,
        refreshToken: data.refreshToken,
      };
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  /**
   * Start signup process - sends OTP to email
   */
  async signup(credentials: {
    email: string;
    password: string;
    firstName?: string;
    lastName?: string;
  }): Promise<{ sessionId: string }> {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/signup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });

      if (!response.ok) {
        let errorMessage = 'Signup failed';
        try {
          const errorData = await response.json();
          // Handle nested message structure from NestJS
          if (typeof errorData.message === 'string') {
            errorMessage = errorData.message;
          } else if (
            typeof errorData.message === 'object' &&
            errorData.message.message
          ) {
            errorMessage = errorData.message.message;
          } else if (errorData.error) {
            errorMessage = errorData.error;
          }
        } catch {
          // If response is not JSON, use status text
          errorMessage = response.statusText || errorMessage;
        }
        throw new Error(errorMessage);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Signup error:', error);
      throw error;
    }
  }

  /**
   * Verify OTP and complete user registration
   */
  async verifyOtp(
    sessionId: string,
    otp: string,
    password: string,
    firstName?: string,
    lastName?: string
  ): Promise<AuthResponse> {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/verify-otp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Session: sessionId,
        },
        body: JSON.stringify({
          otp,
          password,
          firstName,
          lastName,
        }),
      });

      if (!response.ok) {
        let errorMessage = 'OTP verification failed';
        try {
          const errorData = await response.json();
          // Handle nested message structure from NestJS
          if (typeof errorData.message === 'string') {
            errorMessage = errorData.message;
          } else if (
            typeof errorData.message === 'object' &&
            errorData.message.message
          ) {
            errorMessage = errorData.message.message;
          } else if (errorData.error) {
            errorMessage = errorData.error;
          }
        } catch {
          // If response is not JSON, use status text
          errorMessage = response.statusText || errorMessage;
        }
        throw new Error(errorMessage);
      }

      const data: AuthResponse = await response.json();

      // Store token in localStorage and instance
      this.token = data.accessToken;
      localStorage.setItem('auth_token', data.accessToken);
      localStorage.setItem('refresh_token', data.refreshToken);
      localStorage.setItem('user', JSON.stringify(data.user));

      return {
        user: data.user,
        token: data.accessToken,
        refreshToken: data.refreshToken,
      };
    } catch (error) {
      console.error('OTP verification error:', error);
      throw error;
    }
  }

  /**
   * Logout user and clear stored data
   */
  async logout(): Promise<void> {
    try {
      if (this.token) {
        await fetch(`${API_BASE_URL}/auth/logout`, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${this.token}`,
            'Content-Type': 'application/json',
          },
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear all stored data regardless of API call success
      this.token = null;
      this.clearAuthData();
    }
  }

  /**
   * Get current authentication token
   */
  getToken(): string | null {
    return this.token;
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return !!this.token;
  }

  /**
   * Get current user from localStorage
   */
  getCurrentUser() {
    try {
      const userStr = localStorage.getItem('user');
      if (!userStr || userStr === 'undefined' || userStr === 'null') {
        return null;
      }
      return JSON.parse(userStr);
    } catch (error) {
      console.error('Error parsing user from localStorage:', error);
      // Clear invalid data
      localStorage.removeItem('user');
      return null;
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(): Promise<string> {
    const refreshToken = localStorage.getItem('refresh_token');

    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await fetch(`${API_BASE_URL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken }),
      });

      if (!response.ok) {
        throw new Error('Token refresh failed');
      }

      const data = await response.json();
      this.token = data.accessToken;
      localStorage.setItem('auth_token', data.accessToken);

      return data.accessToken;
    } catch (error) {
      // If refresh fails, logout user
      await this.logout();
      throw error;
    }
  }

  /**
   * Make authenticated API request
   */
  async authenticatedRequest(
    url: string,
    options: RequestInit = {}
  ): Promise<Response> {
    const token = this.getToken();

    if (!token) {
      throw new Error('No authentication token available');
    }

    const response = await fetch(url, {
      ...options,
      headers: {
        ...options.headers,
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    // If token is expired, try to refresh
    if (response.status === 401) {
      try {
        await this.refreshToken();
        // Retry the request with new token
        return await fetch(url, {
          ...options,
          headers: {
            ...options.headers,
            Authorization: `Bearer ${this.getToken()}`,
            'Content-Type': 'application/json',
          },
        });
      } catch {
        throw new Error('Authentication failed');
      }
    }

    return response;
  }
}

export default AuthService.getInstance();
