const API_BASE_URL =
  import.meta.env.VITE_INTERNAL_DASHBOARD_API_URL || 'http://localhost:3006';

export interface User {
  _id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  roles: Role[];
  isActive: boolean;
  isEmailConfirmed: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Role {
  _id: string;
  name: string;
  description: string;
  permissions: Permission[];
  isActive: boolean;
  canRegister: boolean;
}

export interface Permission {
  _id: string;
  name: string;
  description: string;
  isActive: boolean;
}

export interface CreateUserRequest {
  email: string;
  password: string;
  roleNames?: string[];
}

export interface AssignRoleRequest {
  roleNames: string[];
}

export interface CreateRoleRequest {
  name: string;
  description: string;
  permissions: string[];
}

class UserManagementService {
  private getAuthHeaders() {
    const token = localStorage.getItem('token');
    return {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    };
  }

  // User Management
  async createUser(userData: CreateUserRequest): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/admin/users/create-user`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(userData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to create user');
    }
  }

  async blacklistUser(userId: string, reason: string): Promise<void> {
    const response = await fetch(
      `${API_BASE_URL}/admin/users/${userId}/blacklist`,
      {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({ reason }),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to blacklist user');
    }
  }

  async unblacklistUser(userId: string): Promise<void> {
    const response = await fetch(
      `${API_BASE_URL}/admin/users/${userId}/unblacklist`,
      {
        method: 'POST',
        headers: this.getAuthHeaders(),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to unblacklist user');
    }
  }

  async revokeUserToken(userId: string): Promise<void> {
    const response = await fetch(
      `${API_BASE_URL}/admin/users/${userId}/revoke-token`,
      {
        method: 'POST',
        headers: this.getAuthHeaders(),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to revoke user token');
    }
  }

  // Role Assignment
  async assignRolesToUser(
    userId: string,
    roleData: AssignRoleRequest
  ): Promise<void> {
    const response = await fetch(
      `${API_BASE_URL}/admin/users/${userId}/roles`,
      {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(roleData),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to assign roles');
    }
  }

  async addRolesToUser(
    userId: string,
    roleData: AssignRoleRequest
  ): Promise<void> {
    const response = await fetch(
      `${API_BASE_URL}/admin/users/${userId}/roles`,
      {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(roleData),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to add roles');
    }
  }

  async removeRolesFromUser(
    userId: string,
    roleData: AssignRoleRequest
  ): Promise<void> {
    const response = await fetch(
      `${API_BASE_URL}/admin/users/${userId}/roles`,
      {
        method: 'DELETE',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(roleData),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to remove roles');
    }
  }

  // Role Management
  async getAllRoles(): Promise<Role[]> {
    const response = await fetch(`${API_BASE_URL}/roles`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch roles');
    }

    return response.json();
  }

  async createRole(roleData: CreateRoleRequest): Promise<Role> {
    const response = await fetch(`${API_BASE_URL}/roles`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(roleData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to create role');
    }

    return response.json();
  }

  async getRoleById(roleId: string): Promise<Role> {
    const response = await fetch(`${API_BASE_URL}/roles/${roleId}`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch role');
    }

    return response.json();
  }

  async updateRole(
    roleId: string,
    roleData: Partial<CreateRoleRequest>
  ): Promise<Role> {
    const response = await fetch(`${API_BASE_URL}/roles/${roleId}`, {
      method: 'PATCH',
      headers: this.getAuthHeaders(),
      body: JSON.stringify(roleData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update role');
    }

    return response.json();
  }

  // Permission Management
  async getAllPermissions(): Promise<Permission[]> {
    const response = await fetch(`${API_BASE_URL}/permissions`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch permissions');
    }

    return response.json();
  }

  // User Permissions
  async getUserPermissions(): Promise<{
    permissions: string[];
    roles: { id: string; name: string; permissions: string[] }[];
  }> {
    const response = await fetch(`${API_BASE_URL}/users/permissions`, {
      method: 'GET',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to fetch user permissions');
    }

    return response.json();
  }

  async checkPermissions(permissions: string[]): Promise<{
    hasPermission: boolean;
    matchedPermissions: string[];
    requiredPermissions: string[];
  }> {
    const response = await fetch(`${API_BASE_URL}/users/check-permissions`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
      body: JSON.stringify({ permissions }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to check permissions');
    }

    return response.json();
  }
}

export default new UserManagementService();
