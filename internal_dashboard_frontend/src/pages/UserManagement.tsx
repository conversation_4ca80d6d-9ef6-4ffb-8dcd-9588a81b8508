import { useState, useEffect } from 'react';
import { Button } from '../components/ui';
import userManagementService, { User, Role } from '../services/userManagement';
import CreateUserModal from '../components/modals/CreateUserModal';
import AssignRoleModal from '../components/modals/AssignRoleModal';
import CreateRoleModal from '../components/modals/CreateRoleModal';

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Modal states
  const [showCreateUser, setShowCreateUser] = useState(false);
  const [showAssignRole, setShowAssignRole] = useState(false);
  const [showCreateRole, setShowCreateRole] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Load roles first as they're needed for user creation
      const rolesData = await userManagementService.getAllRoles();
      setRoles(rolesData);
      
      // Note: There's no direct API to list all users in the backend
      // We'll need to implement this or work with the available APIs
      setUsers([]); // Placeholder until we have a user listing API
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateUser = async (userData: { email: string; password: string; roleNames?: string[] }) => {
    try {
      await userManagementService.createUser(userData);
      setShowCreateUser(false);
      // Refresh data or add user to list
      await loadData();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create user');
    }
  };

  const handleBlacklistUser = async (userId: string, reason: string) => {
    try {
      await userManagementService.blacklistUser(userId, reason);
      // Update user status in the list
      setUsers(users.map(user => 
        user._id === userId ? { ...user, isActive: false } : user
      ));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to blacklist user');
    }
  };

  const handleUnblacklistUser = async (userId: string) => {
    try {
      await userManagementService.unblacklistUser(userId);
      // Update user status in the list
      setUsers(users.map(user => 
        user._id === userId ? { ...user, isActive: true } : user
      ));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to unblacklist user');
    }
  };

  const handleAssignRoles = async (userId: string, roleNames: string[]) => {
    try {
      await userManagementService.assignRolesToUser(userId, { roleNames });
      setShowAssignRole(false);
      setSelectedUser(null);
      // Refresh data to get updated user roles
      await loadData();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to assign roles');
    }
  };

  const handleCreateRole = async (roleData: { name: string; description: string; permissions: string[] }) => {
    try {
      await userManagementService.createRole(roleData);
      setShowCreateRole(false);
      // Refresh roles list
      const rolesData = await userManagementService.getAllRoles();
      setRoles(rolesData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create role');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
          <p className="text-gray-600">Manage users, roles, and permissions</p>
        </div>
        <div className="flex space-x-3">
          <Button
            onClick={() => setShowCreateRole(true)}
            variant="outline"
          >
            Create Role
          </Button>
          <Button
            onClick={() => setShowCreateUser(true)}
            variant="primary"
          >
            Create User
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
            <div className="ml-auto pl-3">
              <button
                onClick={() => setError(null)}
                className="text-red-400 hover:text-red-600"
              >
                <span className="sr-only">Dismiss</span>
                <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Roles Section */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Available Roles</h2>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {roles.map((role) => (
              <div key={role._id} className="border border-gray-200 rounded-lg p-4">
                <h3 className="font-medium text-gray-900">{role.name}</h3>
                <p className="text-sm text-gray-600 mt-1">{role.description}</p>
                <div className="mt-2">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    role.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {role.isActive ? 'Active' : 'Inactive'}
                  </span>
                  {role.canRegister && (
                    <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      Registrable
                    </span>
                  )}
                </div>
                <div className="mt-2">
                  <p className="text-xs text-gray-500">
                    {role.permissions?.length || 0} permissions
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Users Section */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Users</h2>
        </div>
        <div className="p-6">
          {users.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">No users found. Create your first user to get started.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      User
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Roles
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {users.map((user) => (
                    <tr key={user._id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{user.email}</div>
                          {(user.firstName || user.lastName) && (
                            <div className="text-sm text-gray-500">
                              {user.firstName} {user.lastName}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-wrap gap-1">
                          {user.roles?.map((role) => (
                            <span
                              key={role._id}
                              className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                            >
                              {role.name}
                            </span>
                          ))}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {user.isActive ? 'Active' : 'Disabled'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => {
                              setSelectedUser(user);
                              setShowAssignRole(true);
                            }}
                            className="text-primary-600 hover:text-primary-900"
                          >
                            Assign Role
                          </button>
                          {user.isActive ? (
                            <button
                              onClick={() => handleBlacklistUser(user._id, 'Disabled by admin')}
                              className="text-red-600 hover:text-red-900"
                            >
                              Disable
                            </button>
                          ) : (
                            <button
                              onClick={() => handleUnblacklistUser(user._id)}
                              className="text-green-600 hover:text-green-900"
                            >
                              Enable
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Modals */}
      {showCreateUser && (
        <CreateUserModal
          isOpen={showCreateUser}
          onClose={() => setShowCreateUser(false)}
          onSubmit={handleCreateUser}
          availableRoles={roles}
        />
      )}

      {showAssignRole && selectedUser && (
        <AssignRoleModal
          isOpen={showAssignRole}
          onClose={() => {
            setShowAssignRole(false);
            setSelectedUser(null);
          }}
          onSubmit={(roleNames) => handleAssignRoles(selectedUser._id, roleNames)}
          user={selectedUser}
          availableRoles={roles}
        />
      )}

      {showCreateRole && (
        <CreateRoleModal
          isOpen={showCreateRole}
          onClose={() => setShowCreateRole(false)}
          onSubmit={handleCreateRole}
        />
      )}
    </div>
  );
};

export default UserManagement;
