import { useState, useEffect } from 'react';
import { Button, Input } from '../components/ui';
import { useAuth } from '../hooks/useAuth';
import { sanitizeInput } from '../utils/validation';
import { cn } from '../utils';

interface VerifyOtpProps {
  sessionId: string;
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  onBack: () => void;
}

const VerifyOtp: React.FC<VerifyOtpProps> = ({
  sessionId,
  email,
  password,
  firstName,
  lastName,
  onBack,
}) => {
  const [otp, setOtp] = useState('');
  const [formErrors, setFormErrors] = useState<{
    otp: string | null;
  }>({
    otp: null,
  });

  const { verifyOtp, isLoading, error, success, clearError, clearSuccess } =
    useAuth();

  // Clear messages when user starts typing
  useEffect(() => {
    if (error || success) {
      clearError();
      clearSuccess();
    }
  }, [otp, error, success, clearError, clearSuccess]);

  const validateOtp = (otpValue: string) => {
    if (!otpValue.trim()) {
      return 'OTP is required';
    }
    if (otpValue.length !== 6) {
      return 'OTP must be 6 digits';
    }
    if (!/^\d{6}$/.test(otpValue)) {
      return 'OTP must contain only numbers';
    }
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Sanitize input
    const sanitizedOtp = sanitizeInput(otp);

    // Validate OTP
    const otpError = validateOtp(sanitizedOtp);
    setFormErrors({ otp: otpError });

    if (otpError) {
      return;
    }

    try {
      await verifyOtp(sessionId, sanitizedOtp, password, firstName, lastName);
    } catch (err) {
      console.error('OTP verification failed:', err);
    }
  };

  const handleOtpChange = (value: string) => {
    // Only allow numbers and limit to 6 digits
    const numericValue = value.replace(/\D/g, '').slice(0, 6);
    setOtp(numericValue);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        {/* Logo */}
        <div className="flex justify-center">
          <div className="flex h-12 w-12 items-center justify-center">
            <img src="/favicon.svg" alt="NexQloud" className="h-12 w-12" />
          </div>
        </div>

        {/* Admin Panel v2.0 Watermark */}
        <div className="mt-3 text-center">
          <span className="inline-flex items-center rounded-full bg-primary-50 px-3 py-1 text-xs font-medium text-primary-700 ring-1 ring-inset ring-primary-600/20">
            <svg
              className="mr-1.5 h-3 w-3"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z"
                clipRule="evenodd"
              />
            </svg>
            Admin Panel v2.0
          </span>
        </div>

        {/* Header */}
        <h2 className="mt-6 text-center text-3xl font-bold tracking-tight text-gray-900">
          Verify your email
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          We sent a verification code to{' '}
          <span className="font-medium">{email}</span>
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="card card-body">
          <form className="space-y-6" onSubmit={handleSubmit}>
            {/* Server Error */}
            {error && (
              <div className="rounded-lg border border-error-200 bg-error-50 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg
                      className="h-5 w-5 text-error-400"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-error-800">{error}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Success Message */}
            {success && (
              <div className="rounded-lg border border-success-200 bg-success-50 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg
                      className="h-5 w-5 text-success-400"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L7.53 10.53a.75.75 0 00-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-success-800">{success}</p>
                  </div>
                </div>
              </div>
            )}

            {/* OTP Field */}
            <div className="form-group">
              <label className="form-label form-label-required">
                Verification Code
              </label>
              <input
                type="text"
                value={otp}
                onChange={e => handleOtpChange(e.target.value)}
                placeholder="Enter 6-digit code"
                disabled={isLoading}
                required
                maxLength={6}
                className={cn(
                  'input text-center text-2xl tracking-widest',
                  formErrors.otp && 'input-error'
                )}
                autoComplete="one-time-code"
              />
              {formErrors.otp && <p className="form-error">{formErrors.otp}</p>}
              <p className="mt-2 text-sm text-gray-500">
                Enter the 6-digit code sent to your email
              </p>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              variant="primary"
              loading={isLoading}
              disabled={isLoading || otp.length !== 6}
              className="w-full"
            >
              Verify & Create Account
            </Button>
          </form>

          {/* Back Link */}
          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-200" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="bg-white px-2 text-gray-500">
                  Didn't receive the code?
                </span>
              </div>
            </div>

            <div className="mt-6 text-center space-y-2">
              <button
                type="button"
                onClick={onBack}
                className="font-semibold text-primary-600 hover:text-primary-500 block w-full"
              >
                Go back to signup
              </button>
              <p className="text-sm text-gray-500">
                Check your spam folder or try again
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VerifyOtp;
