import { useState } from 'react';
import Login from '../pages/Login';
import Signup from '../pages/Signup';
import VerifyOtp from '../pages/VerifyOtp';

type AuthView = 'login' | 'signup' | 'verify-otp';

interface SignupData {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  sessionId?: string;
}

const Auth: React.FC = () => {
  const [currentView, setCurrentView] = useState<AuthView>('login');
  const [signupData, setSignupData] = useState<SignupData>({
    email: '',
    password: '',
  });

  const handleSwitchToSignup = () => {
    setCurrentView('signup');
  };

  const handleSwitchToLogin = () => {
    setCurrentView('login');
    setSignupData({ email: '', password: '' });
  };

  const handleSignupSuccess = (data: SignupData) => {
    setSignupData(data);
    setCurrentView('verify-otp');
  };

  const handleBackToSignup = () => {
    setCurrentView('signup');
  };

  switch (currentView) {
    case 'login':
      return <Login onSwitchToSignup={handleSwitchToSignup} />;
    
    case 'signup':
      return (
        <Signup 
          onSwitchToLogin={handleSwitchToLogin}
          onSignupSuccess={handleSignupSuccess}
        />
      );
    
    case 'verify-otp':
      return (
        <VerifyOtp
          sessionId={signupData.sessionId!}
          email={signupData.email}
          password={signupData.password}
          firstName={signupData.firstName}
          lastName={signupData.lastName}
          onBack={handleBackToSignup}
        />
      );
    
    default:
      return <Login onSwitchToSignup={handleSwitchToSignup} />;
  }
};

export default Auth;
