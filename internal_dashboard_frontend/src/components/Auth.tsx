import { useLocation } from 'react-router-dom';
import Login from '../pages/Login';
import Signup from '../pages/Signup';
import VerifyOtp from '../pages/VerifyOtp';

const Auth: React.FC = () => {
  const location = useLocation();

  // Determine which auth component to show based on route
  switch (location.pathname) {
    case '/signup':
      return <Signup />;

    case '/verify-otp':
      return <VerifyOtp />;

    case '/login':
    default:
      return <Login />;
  }
};

export default Auth;
