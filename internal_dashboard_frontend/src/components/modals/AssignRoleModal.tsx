import { useState } from 'react';
import { Button } from '../ui';
import { User, Role } from '../../services/userManagement';

interface AssignRoleModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (roleNames: string[]) => void;
  user: User;
  availableRoles: Role[];
}

const AssignRoleModal: React.FC<AssignRoleModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  user,
  availableRoles,
}) => {
  const [selectedRoles, setSelectedRoles] = useState<string[]>(
    user.roles?.map(role => role.name) || []
  );
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setLoading(true);
    try {
      await onSubmit(selectedRoles);
    } catch (error) {
      // Error handling is done in parent component
    } finally {
      setLoading(false);
    }
  };

  const handleRoleToggle = (roleName: string) => {
    setSelectedRoles(prev => 
      prev.includes(roleName)
        ? prev.filter(r => r !== roleName)
        : [...prev, roleName]
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        ></div>

        {/* Modal */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <form onSubmit={handleSubmit}>
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="sm:flex sm:items-start">
                <div className="w-full">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                    Assign Roles to User
                  </h3>
                  
                  {/* User Info */}
                  <div className="mb-4 p-3 bg-gray-50 rounded-md">
                    <p className="text-sm font-medium text-gray-900">{user.email}</p>
                    {(user.firstName || user.lastName) && (
                      <p className="text-sm text-gray-600">
                        {user.firstName} {user.lastName}
                      </p>
                    )}
                  </div>

                  {/* Current Roles */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Current Roles
                    </label>
                    <div className="flex flex-wrap gap-2">
                      {user.roles && user.roles.length > 0 ? (
                        user.roles.map((role) => (
                          <span
                            key={role._id}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                          >
                            {role.name}
                          </span>
                        ))
                      ) : (
                        <span className="text-sm text-gray-500">No roles assigned</span>
                      )}
                    </div>
                  </div>

                  {/* Available Roles */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Select Roles
                    </label>
                    <div className="space-y-2 max-h-60 overflow-y-auto border border-gray-200 rounded-md p-3">
                      {availableRoles.filter(role => role.isActive).map((role) => (
                        <label key={role._id} className="flex items-start">
                          <input
                            type="checkbox"
                            checked={selectedRoles.includes(role.name)}
                            onChange={() => handleRoleToggle(role.name)}
                            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded mt-0.5"
                          />
                          <div className="ml-3">
                            <span className="text-sm font-medium text-gray-900">{role.name}</span>
                            <p className="text-xs text-gray-500">{role.description}</p>
                            {role.permissions && role.permissions.length > 0 && (
                              <p className="text-xs text-gray-400 mt-1">
                                {role.permissions.length} permission{role.permissions.length !== 1 ? 's' : ''}
                              </p>
                            )}
                          </div>
                        </label>
                      ))}
                    </div>
                    {availableRoles.filter(role => role.isActive).length === 0 && (
                      <p className="text-sm text-gray-500">No active roles available</p>
                    )}
                  </div>

                  {/* Selected Roles Preview */}
                  {selectedRoles.length > 0 && (
                    <div className="mt-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Selected Roles ({selectedRoles.length})
                      </label>
                      <div className="flex flex-wrap gap-2">
                        {selectedRoles.map((roleName) => (
                          <span
                            key={roleName}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                          >
                            {roleName}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <Button
                type="submit"
                variant="primary"
                disabled={loading}
                className="w-full sm:w-auto sm:ml-3"
              >
                {loading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Assigning...
                  </div>
                ) : (
                  'Assign Roles'
                )}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={loading}
                className="mt-3 w-full sm:mt-0 sm:w-auto"
              >
                Cancel
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AssignRoleModal;
