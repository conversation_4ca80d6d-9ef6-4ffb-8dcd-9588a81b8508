import { useState, useEffect } from 'react';
import { Button, Input } from '../ui';
import userManagementService, { Permission } from '../../services/userManagement';

interface CreateRoleModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (roleData: { name: string; description: string; permissions: string[] }) => void;
}

const CreateRoleModal: React.FC<CreateRoleModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    selectedPermissions: [] as string[],
  });
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingPermissions, setLoadingPermissions] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  useEffect(() => {
    if (isOpen) {
      loadPermissions();
    }
  }, [isOpen]);

  const loadPermissions = async () => {
    try {
      setLoadingPermissions(true);
      const permissionsData = await userManagementService.getAllPermissions();
      setPermissions(permissionsData);
    } catch (error) {
      console.error('Failed to load permissions:', error);
    } finally {
      setLoadingPermissions(false);
    }
  };

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Role name is required';
    } else if (!/^[a-zA-Z0-9_-]+$/.test(formData.name.trim())) {
      newErrors.name = 'Role name can only contain letters, numbers, hyphens, and underscores';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await onSubmit({
        name: formData.name.trim(),
        description: formData.description.trim(),
        permissions: formData.selectedPermissions,
      });
      
      // Reset form
      setFormData({
        name: '',
        description: '',
        selectedPermissions: [],
      });
      setErrors({});
    } catch (error) {
      // Error handling is done in parent component
    } finally {
      setLoading(false);
    }
  };

  const handlePermissionToggle = (permissionId: string) => {
    setFormData(prev => ({
      ...prev,
      selectedPermissions: prev.selectedPermissions.includes(permissionId)
        ? prev.selectedPermissions.filter(p => p !== permissionId)
        : [...prev.selectedPermissions, permissionId]
    }));
  };

  const groupedPermissions = permissions.reduce((groups, permission) => {
    const category = permission.name.split(':')[0] || 'other';
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(permission);
    return groups;
  }, {} as { [key: string]: Permission[] });

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        ></div>

        {/* Modal */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
          <form onSubmit={handleSubmit}>
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="sm:flex sm:items-start">
                <div className="w-full">
                  <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                    Create New Role
                  </h3>
                  
                  <div className="space-y-4">
                    {/* Role Name */}
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                        Role Name
                      </label>
                      <Input
                        id="name"
                        type="text"
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="e.g., content_manager"
                        error={errors.name}
                        className="mt-1"
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        Use lowercase letters, numbers, hyphens, and underscores only
                      </p>
                    </div>

                    {/* Description */}
                    <div>
                      <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                        Description
                      </label>
                      <textarea
                        id="description"
                        value={formData.description}
                        onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                        placeholder="Describe what this role is for..."
                        rows={3}
                        className={`mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm ${
                          errors.description ? 'border-red-300' : ''
                        }`}
                      />
                      {errors.description && (
                        <p className="mt-1 text-xs text-red-600">{errors.description}</p>
                      )}
                    </div>

                    {/* Permissions */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Permissions
                      </label>
                      
                      {loadingPermissions ? (
                        <div className="flex items-center justify-center py-4">
                          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
                          <span className="ml-2 text-sm text-gray-500">Loading permissions...</span>
                        </div>
                      ) : (
                        <div className="max-h-60 overflow-y-auto border border-gray-200 rounded-md p-3">
                          {Object.keys(groupedPermissions).map((category) => (
                            <div key={category} className="mb-4 last:mb-0">
                              <h4 className="text-sm font-medium text-gray-900 mb-2 capitalize">
                                {category} Permissions
                              </h4>
                              <div className="space-y-2 ml-4">
                                {groupedPermissions[category].map((permission) => (
                                  <label key={permission._id} className="flex items-start">
                                    <input
                                      type="checkbox"
                                      checked={formData.selectedPermissions.includes(permission._id)}
                                      onChange={() => handlePermissionToggle(permission._id)}
                                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded mt-0.5"
                                    />
                                    <div className="ml-3">
                                      <span className="text-sm font-medium text-gray-900">
                                        {permission.name}
                                      </span>
                                      <p className="text-xs text-gray-500">{permission.description}</p>
                                    </div>
                                  </label>
                                ))}
                              </div>
                            </div>
                          ))}
                          
                          {permissions.length === 0 && !loadingPermissions && (
                            <p className="text-sm text-gray-500">No permissions available</p>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Selected Permissions Summary */}
                    {formData.selectedPermissions.length > 0 && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Selected Permissions ({formData.selectedPermissions.length})
                        </label>
                        <div className="flex flex-wrap gap-2">
                          {formData.selectedPermissions.map((permissionId) => {
                            const permission = permissions.find(p => p._id === permissionId);
                            return permission ? (
                              <span
                                key={permissionId}
                                className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                              >
                                {permission.name}
                              </span>
                            ) : null;
                          })}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <Button
                type="submit"
                variant="primary"
                disabled={loading || loadingPermissions}
                className="w-full sm:w-auto sm:ml-3"
              >
                {loading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Creating...
                  </div>
                ) : (
                  'Create Role'
                )}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={loading}
                className="mt-3 w-full sm:mt-0 sm:w-auto"
              >
                Cancel
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default CreateRoleModal;
