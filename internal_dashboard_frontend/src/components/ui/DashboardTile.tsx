import { cn } from '../../utils';

interface DashboardTileProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  stats?: {
    value: string | number;
    label: string;
    trend?: {
      value: number;
      isPositive: boolean;
    };
  };
  onClick?: () => void;
  className?: string;
  badge?: string;
}

const DashboardTile: React.FC<DashboardTileProps> = ({
  title,
  description,
  icon,
  stats,
  onClick,
  className,
  badge,
}) => {
  return (
    <div
      className={cn(
        'relative bg-white rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-all duration-200 cursor-pointer group',
        'hover:border-primary-300 hover:-translate-y-1',
        className
      )}
      onClick={onClick}
    >
      {/* Badge */}
      {badge && (
        <div className="absolute top-4 right-4">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
            {badge}
          </span>
        </div>
      )}

      {/* Icon */}
      <div className="flex items-center justify-center h-12 w-12 rounded-lg bg-primary-50 text-primary-600 group-hover:bg-primary-100 transition-colors duration-200">
        {icon}
      </div>

      {/* Content */}
      <div className="mt-4">
        <h3 className="text-lg font-semibold text-gray-900 group-hover:text-primary-700 transition-colors duration-200">
          {title}
        </h3>
        <p className="mt-2 text-sm text-gray-600 line-clamp-2">
          {description}
        </p>

        {/* Stats */}
        {stats && (
          <div className="mt-4 pt-4 border-t border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-gray-900">{stats.value}</div>
                <div className="text-sm text-gray-500">{stats.label}</div>
              </div>
              {stats.trend && (
                <div className="flex items-center">
                  <span
                    className={cn(
                      'inline-flex items-center text-sm font-medium',
                      stats.trend.isPositive ? 'text-green-600' : 'text-red-600'
                    )}
                  >
                    {stats.trend.isPositive ? (
                      <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 18L9 11.25l4.306 4.307a11.95 11.95 0 015.814-5.519l2.74-1.22m0 0l-5.94-2.28m5.94 2.28l-2.28 5.941" />
                      </svg>
                    ) : (
                      <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 6L9 12.75l4.286-4.286a11.948 11.948 0 014.306 6.43l.776 2.898m0 0l3.182-5.511m-3.182 5.511l-5.511-3.182" />
                      </svg>
                    )}
                    {Math.abs(stats.trend.value)}%
                  </span>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Hover arrow */}
      <div className="absolute top-6 right-6 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
        <svg className="h-5 w-5 text-primary-600" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 19.5l15-15m0 0H8.25m11.25 0v11.25" />
        </svg>
      </div>
    </div>
  );
};

export default DashboardTile;
