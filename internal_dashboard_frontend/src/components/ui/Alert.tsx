import { cn } from '../../utils';

interface AlertProps {
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  actions?: React.ReactNode;
  onClose?: () => void;
  className?: string;
}

const Alert: React.FC<AlertProps> = ({
  type,
  title,
  message,
  actions,
  onClose,
  className
}) => {
  const getAlertStyles = () => {
    const baseStyles = 'rounded-lg border p-4';
    
    switch (type) {
      case 'success':
        return cn(baseStyles, 'border-green-200 bg-green-50');
      case 'error':
        return cn(baseStyles, 'border-red-200 bg-red-50');
      case 'warning':
        return cn(baseStyles, 'border-yellow-200 bg-yellow-50');
      case 'info':
        return cn(baseStyles, 'border-blue-200 bg-blue-50');
    }
  };

  const getIcon = () => {
    const iconClasses = 'h-5 w-5';
    
    switch (type) {
      case 'success':
        return (
          <svg className={cn(iconClasses, 'text-green-400')} viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.236 4.53L7.53 10.53a.75.75 0 00-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clipRule="evenodd" />
          </svg>
        );
      case 'error':
        return (
          <svg className={cn(iconClasses, 'text-red-400')} viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clipRule="evenodd" />
          </svg>
        );
      case 'warning':
        return (
          <svg className={cn(iconClasses, 'text-yellow-400')} viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
          </svg>
        );
      case 'info':
        return (
          <svg className={cn(iconClasses, 'text-blue-400')} viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z" clipRule="evenodd" />
          </svg>
        );
    }
  };

  const getTextColors = () => {
    switch (type) {
      case 'success':
        return { title: 'text-green-800', message: 'text-green-700' };
      case 'error':
        return { title: 'text-red-800', message: 'text-red-700' };
      case 'warning':
        return { title: 'text-yellow-800', message: 'text-yellow-700' };
      case 'info':
        return { title: 'text-blue-800', message: 'text-blue-700' };
    }
  };

  const textColors = getTextColors();

  return (
    <div className={cn(getAlertStyles(), className)}>
      <div className="flex">
        <div className="flex-shrink-0">
          {getIcon()}
        </div>
        <div className="ml-3 flex-1">
          {title && (
            <h3 className={cn('text-sm font-medium', textColors.title)}>
              {title}
            </h3>
          )}
          <div className={cn('text-sm', title ? 'mt-1' : '', textColors.message)}>
            {message}
          </div>
          {actions && (
            <div className="mt-3">
              {actions}
            </div>
          )}
        </div>
        {onClose && (
          <div className="ml-auto pl-3">
            <div className="-mx-1.5 -my-1.5">
              <button
                type="button"
                className={cn(
                  'inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2',
                  type === 'success' && 'bg-green-50 text-green-500 hover:bg-green-100 focus:ring-green-600',
                  type === 'error' && 'bg-red-50 text-red-500 hover:bg-red-100 focus:ring-red-600',
                  type === 'warning' && 'bg-yellow-50 text-yellow-500 hover:bg-yellow-100 focus:ring-yellow-600',
                  type === 'info' && 'bg-blue-50 text-blue-500 hover:bg-blue-100 focus:ring-blue-600'
                )}
                onClick={onClose}
              >
                <span className="sr-only">Dismiss</span>
                <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M6.28 5.22a.75.75 0 00-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 101.06 1.06L10 11.06l3.72 3.72a.75.75 0 101.06-1.06L11.06 10l3.72-3.72a.75.75 0 00-1.06-1.06L10 8.94 6.28 5.22z" />
                </svg>
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Alert;
