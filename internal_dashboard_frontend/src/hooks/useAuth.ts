import { useState, useCallback } from 'react';
import authService from '../services/auth';
import { useToast } from '../contexts/ToastContext';
import type {
  LoginCredentials,
  AuthResponse,
  User,
  SignupCredentials,
  SignupResponse,
} from '../types';

interface UseAuthReturn {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  success: string | null;
  login: (credentials: LoginCredentials) => Promise<void>;
  signup: (credentials: SignupCredentials) => Promise<SignupResponse>;
  verifyOtp: (
    sessionId: string,
    otp: string,
    password: string,
    firstName?: string,
    lastName?: string
  ) => Promise<void>;
  logout: () => Promise<void>;
  clearError: () => void;
  clearSuccess: () => void;
}

export function useAuth(): UseAuthReturn {
  const [user, setUser] = useState<User | null>(authService.getCurrentUser());
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const { showSuccess, showError } = useToast();

  const login = useCallback(
    async (credentials: LoginCredentials) => {
      try {
        setIsLoading(true);
        setError(null);
        setSuccess(null);

        const response: AuthResponse = await authService.login(credentials);
        setUser(response.user);

        // Show success toast
        showSuccess(`Welcome back, ${response.user.firstName || 'User'}!`);
        setSuccess('Login successful! Redirecting to dashboard...');

        // Small delay to show success message before redirect
        setTimeout(() => {
          // Navigation will be handled by React Router in the App component
          // The route protection will automatically redirect to /home
        }, 1000);
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Login failed';
        setError(errorMessage);
        showError(errorMessage);
        throw err; // Re-throw so the component can handle it if needed
      } finally {
        setIsLoading(false);
      }
    },
    [showSuccess, showError]
  );

  const signup = useCallback(
    async (credentials: SignupCredentials) => {
      try {
        setIsLoading(true);
        setError(null);
        setSuccess(null);

        const response: SignupResponse = await authService.signup(credentials);
        const successMessage =
          'Verification code sent to your email. Please check your inbox.';
        setSuccess(successMessage);
        showSuccess(successMessage);
        return response;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Signup failed';
        setError(errorMessage);
        showError(errorMessage);
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [showSuccess, showError]
  );

  const verifyOtp = useCallback(
    async (
      sessionId: string,
      otp: string,
      password: string,
      firstName?: string,
      lastName?: string
    ) => {
      try {
        setIsLoading(true);
        setError(null);
        setSuccess(null);

        const response: AuthResponse = await authService.verifyOtp(
          sessionId,
          otp,
          password,
          firstName,
          lastName
        );
        setUser(response.user);

        // Show success toast
        showSuccess(
          `Welcome to NexQloud, ${response.user.firstName || 'User'}!`
        );
        setSuccess('Account created successfully! Redirecting to dashboard...');

        // Small delay to show success message before redirect
        setTimeout(() => {
          window.location.reload(); // Force a refresh to ensure state is updated
        }, 1000);
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'OTP verification failed';
        setError(errorMessage);
        showError(errorMessage);
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [showSuccess, showError]
  );

  const logout = useCallback(async () => {
    try {
      setIsLoading(true);
      await authService.logout();
      setUser(null);
      setError(null);
      setSuccess(null);
    } catch (err) {
      console.error('Logout error:', err);
      // Even if logout fails, clear local state
      setUser(null);
      setError(null);
      setSuccess(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const clearSuccess = useCallback(() => {
    setSuccess(null);
  }, []);

  return {
    user,
    isAuthenticated: authService.isAuthenticated(),
    isLoading,
    error,
    success,
    login,
    signup,
    verifyOtp,
    logout,
    clearError,
    clearSuccess,
  };
}
