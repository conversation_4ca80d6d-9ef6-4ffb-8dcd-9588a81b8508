/**
 * Email validation using a comprehensive regex pattern
 */
export function validateEmail(email: string): string | null {
  if (!email) {
    return 'Email is required';
  }

  if (email.length > 254) {
    return 'Email is too long';
  }

  // RFC 5322 compliant email regex (simplified but robust)
  const emailRegex =
    /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

  if (!emailRegex.test(email)) {
    return 'Please enter a valid email address';
  }

  return null;
}

/**
 * Password validation for internal dashboard (simplified for admin users)
 */
export function validatePassword(password: string): string | null {
  if (!password) {
    return 'Password is required';
  }

  if (password.length < 6) {
    return 'Password must be at least 6 characters long';
  }

  if (password.length > 128) {
    return 'Password is too long (maximum 128 characters)';
  }

  // Check for common weak passwords
  const commonPasswords = ['password', '123456', 'admin', 'qwerty', 'letmein'];

  if (commonPasswords.includes(password.toLowerCase())) {
    return 'This password is too common. Please choose a stronger password';
  }

  return null;
}

/**
 * Get password strength score (0-4)
 */
export function getPasswordStrength(password: string): {
  score: number;
  label: string;
  color: string;
} {
  if (!password) {
    return { score: 0, label: 'No password', color: 'gray' };
  }

  let score = 0;

  // Length check
  if (password.length >= 8) score++;
  if (password.length >= 12) score++;

  // Character variety checks
  if (/[a-z]/.test(password)) score++;
  if (/[A-Z]/.test(password)) score++;
  if (/\d/.test(password)) score++;
  if (/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password)) score++;

  // Bonus for very long passwords
  if (password.length >= 16) score++;

  // Cap at 4
  score = Math.min(score, 4);

  const strengthMap = {
    0: { label: 'Very weak', color: 'error' },
    1: { label: 'Weak', color: 'error' },
    2: { label: 'Fair', color: 'warning' },
    3: { label: 'Good', color: 'success' },
    4: { label: 'Strong', color: 'success' },
  };

  return {
    score,
    ...strengthMap[score as keyof typeof strengthMap],
  };
}

/**
 * Validate name field
 */
export function validateName(
  name: string,
  fieldName: string,
  required: boolean = false
): string | null {
  if (!name && required) {
    return `${fieldName} is required`;
  }

  if (name && name.length > 50) {
    return `${fieldName} must be less than 50 characters`;
  }

  if (name && !/^[a-zA-Z\s'-]+$/.test(name)) {
    return `${fieldName} can only contain letters, spaces, hyphens, and apostrophes`;
  }

  return null;
}

/**
 * Validate login form data
 */
export function validateLoginForm(
  email: string,
  password: string
): {
  email: string | null;
  password: string | null;
  isValid: boolean;
} {
  const emailError = validateEmail(email);
  const passwordError = password ? null : 'Password is required';

  return {
    email: emailError,
    password: passwordError,
    isValid: !emailError && !passwordError,
  };
}

/**
 * Validate signup form data
 */
export function validateSignupForm(
  email: string,
  password: string,
  confirmPassword: string,
  firstName: string,
  lastName: string
): {
  email: string | null;
  password: string | null;
  confirmPassword: string | null;
  firstName: string | null;
  lastName: string | null;
  isValid: boolean;
} {
  const emailError = validateEmail(email);
  const passwordError = validatePassword(password);
  const firstNameError = validateName(firstName, 'First name', false);
  const lastNameError = validateName(lastName, 'Last name', false);

  let confirmPasswordError: string | null = null;
  if (!confirmPassword) {
    confirmPasswordError = 'Please confirm your password';
  } else if (password !== confirmPassword) {
    confirmPasswordError = 'Passwords do not match';
  }

  const isValid =
    !emailError &&
    !passwordError &&
    !confirmPasswordError &&
    !firstNameError &&
    !lastNameError;

  return {
    email: emailError,
    password: passwordError,
    confirmPassword: confirmPasswordError,
    firstName: firstNameError,
    lastName: lastNameError,
    isValid,
  };
}

/**
 * Sanitize input to prevent XSS
 */
export function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove angle brackets
    .trim(); // Remove leading/trailing whitespace
}
