@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  }

  body {
    @apply bg-white text-gray-900 antialiased;
  }

  * {
    @apply border-gray-200;
  }

  input[type='number']::-webkit-outer-spin-button,
  input[type='number']::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type='number'] {
    -moz-appearance: textfield;
    appearance: textfield;
  }
}

@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center gap-2 rounded-lg border border-transparent px-4 py-2.5 text-sm font-semibold transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-sm {
    @apply px-3 py-2 text-sm;
  }

  .btn-lg {
    @apply px-5 py-3 text-base;
  }

  .btn-primary {
    @apply btn border-primary-600 bg-primary-600 text-white shadow-sm hover:border-primary-700 hover:bg-primary-700 focus-visible:ring-primary-600 active:bg-primary-800;
  }

  .btn-secondary {
    @apply btn border-gray-300 bg-white text-gray-700 shadow-sm hover:bg-gray-50 focus-visible:ring-gray-600 active:bg-gray-100;
  }

  .btn-secondary-gray {
    @apply btn border-gray-300 bg-gray-50 text-gray-700 hover:bg-gray-100 focus-visible:ring-gray-600 active:bg-gray-200;
  }

  .btn-tertiary {
    @apply btn border-transparent bg-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-700 focus-visible:ring-gray-600 active:bg-gray-100;
  }

  .btn-destructive {
    @apply btn border-error-600 bg-error-600 text-white shadow-sm hover:border-error-700 hover:bg-error-700 focus-visible:ring-error-600 active:bg-error-800;
  }

  .btn-destructive-secondary {
    @apply btn border-error-300 bg-white text-error-700 shadow-sm hover:bg-error-50 focus-visible:ring-error-600 active:bg-error-100;
  }

  /* Input Components */
  .input {
    @apply flex h-11 w-full rounded-lg border border-gray-300 bg-white px-3.5 py-2.5 text-base text-gray-900 shadow-sm placeholder:text-gray-500 focus:border-primary-300 focus:outline-none focus:ring-4 focus:ring-primary-100 disabled:cursor-not-allowed disabled:bg-gray-50 disabled:text-gray-500;
  }

  .input-error {
    @apply border-error-300 focus:border-error-300 focus:ring-error-100;
  }

  .input-sm {
    @apply h-9 px-3 py-2 text-base;
  }

  .input-lg {
    @apply h-12 px-4 py-3 text-base;
  }

  /* iOS-specific: Prevent zoom on input focus */
  @supports (-webkit-touch-callout: none) {
    .input,
    .input-sm,
    .input-lg,
    input[type='text'],
    input[type='email'],
    input[type='password'],
    input[type='tel'],
    input[type='url'],
    input[type='search'],
    textarea,
    select {
      font-size: 16px !important;
    }
  }

  /* Form Components */
  .form-group {
    @apply space-y-1.5;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700;
  }

  .form-label-required::after {
    @apply text-error-500;
    content: ' *';
  }

  .form-error {
    @apply text-sm text-error-600;
  }

  .form-hint {
    @apply text-sm text-gray-500;
  }

  /* Card Components */
  .card {
    @apply rounded-xl border border-gray-200 bg-white shadow-sm;
  }

  .card-header {
    @apply border-b border-gray-200 px-6 py-5;
  }

  .card-body {
    @apply px-6 py-5;
  }

  .card-footer {
    @apply border-t border-gray-200 px-6 py-4;
  }

  /* Badge Components */
  .badge {
    @apply inline-flex items-center gap-1 rounded-md px-2 py-1 text-xs font-medium;
  }

  .badge-primary {
    @apply bg-primary-50 text-primary-700;
  }

  .badge-gray {
    @apply bg-gray-100 text-gray-700;
  }

  .badge-success {
    @apply bg-success-50 text-success-700;
  }

  .badge-warning {
    @apply bg-warning-50 text-warning-700;
  }

  .badge-error {
    @apply bg-error-50 text-error-700;
  }

  /* Avatar Components */
  .avatar {
    @apply inline-flex items-center justify-center rounded-full bg-gray-100 font-medium text-gray-600;
  }

  .avatar-sm {
    @apply h-8 w-8 text-sm;
  }

  .avatar-md {
    @apply h-10 w-10 text-sm;
  }

  .avatar-lg {
    @apply h-12 w-12 text-base;
  }

  .avatar-xl {
    @apply h-16 w-16 text-lg;
  }

  /* Divider */
  .divider {
    @apply border-t border-gray-200;
  }

  /* Focus ring utilities */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-600 focus:ring-offset-2;
  }

  .focus-ring-error {
    @apply focus:outline-none focus:ring-2 focus:ring-error-600 focus:ring-offset-2;
  }
}
