import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from 'react-router-dom';
import { useAuth } from './hooks';
import Home from './pages/Home';
import Auth from './components/Auth';

function App() {
  const { isAuthenticated } = useAuth();

  return (
    <Router>
      <Routes>
        {/* Public routes */}
        <Route
          path="/login"
          element={
            !isAuthenticated ? <Auth /> : <Navigate to="/home" replace />
          }
        />
        <Route
          path="/signup"
          element={
            !isAuthenticated ? <Auth /> : <Navigate to="/home" replace />
          }
        />
        <Route
          path="/verify-otp"
          element={
            !isAuthenticated ? <Auth /> : <Navigate to="/home" replace />
          }
        />

        {/* Protected routes */}
        <Route
          path="/home"
          element={
            isAuthenticated ? <Home /> : <Navigate to="/login" replace />
          }
        />

        {/* Default redirects */}
        <Route
          path="/"
          element={
            <Navigate to={isAuthenticated ? '/home' : '/login'} replace />
          }
        />

        {/* Catch all - redirect to appropriate page */}
        <Route
          path="*"
          element={
            <Navigate to={isAuthenticated ? '/home' : '/login'} replace />
          }
        />
      </Routes>
    </Router>
  );
}

export default App;
